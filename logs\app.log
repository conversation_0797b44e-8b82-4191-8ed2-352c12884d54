2025-08-21 16:13:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f1edc57f-6a86-da88-1889-1a003662d93e
2025-08-21 16:13:49 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 16:13:49 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 16:14:03 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f1edc57f-6a86-da88-1889-1a003662d93e
2025-08-21 16:14:03 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f1edc57f-6a86-da88-1889-1a003662d93e
2025-08-21 16:14:03 | WARNING  | app.services.intelligent_question_service:_parse_basic_planning_result_robust:1279 - 鲁棒解析基础题命题规划失败: 无法提取JSON内容
2025-08-21 16:14:15 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 16:25:33 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2baf6a50-709a-5feb-5503-4d93ffbb2419
2025-08-21 16:25:33 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 16:25:51 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 2baf6a50-709a-5feb-5503-4d93ffbb2419
2025-08-21 16:25:51 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：2baf6a50-709a-5feb-5503-4d93ffbb2419
2025-08-21 16:26:09 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 16:33:16 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-21 16:33:16 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-21 16:34:09 | INFO     | app.core.state_manager:create_task:90 - 创建任务: beddf1ce-322e-e1f9-e905-1de7154fda29
2025-08-21 16:34:09 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 16:40:17 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-21 16:40:17 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-21 16:40:25 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c99d2ef1-d348-e43c-5255-3cfa8f3f2729
2025-08-21 16:40:25 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 16:40:25 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 16:40:40 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c99d2ef1-d348-e43c-5255-3cfa8f3f2729
2025-08-21 16:40:40 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c99d2ef1-d348-e43c-5255-3cfa8f3f2729
2025-08-21 16:40:40 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 16:41:06 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 16:43:42 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7cbc9615-fb03-f499-4463-0eba905ef6d0
2025-08-21 16:43:42 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 16:44:04 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 7cbc9615-fb03-f499-4463-0eba905ef6d0
2025-08-21 16:44:04 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：7cbc9615-fb03-f499-4463-0eba905ef6d0
2025-08-21 16:44:47 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 16:51:18 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 67b07c06-9e72-36bf-622f-1bc38e67d9eb
2025-08-21 16:51:18 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 16:51:32 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 67b07c06-9e72-36bf-622f-1bc38e67d9eb
2025-08-21 16:51:32 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：67b07c06-9e72-36bf-622f-1bc38e67d9eb
2025-08-21 16:51:47 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:07:58 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c0e8daa5-2e0c-1083-395b-2b553a450a93
2025-08-21 17:07:58 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:08:11 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c0e8daa5-2e0c-1083-395b-2b553a450a93
2025-08-21 17:08:11 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c0e8daa5-2e0c-1083-395b-2b553a450a93
2025-08-21 17:08:26 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:09:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 34b0b1f6-8c3a-5114-c6c5-44e31df07036
2025-08-21 17:09:06 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:09:06 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 17:09:20 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 34b0b1f6-8c3a-5114-c6c5-44e31df07036
2025-08-21 17:09:20 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：34b0b1f6-8c3a-5114-c6c5-44e31df07036
2025-08-21 17:09:20 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 17:10:09 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:16:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 3094f656-f6f2-e9d4-3163-7536efaf9630
2025-08-21 17:16:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:16:53 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 3094f656-f6f2-e9d4-3163-7536efaf9630
2025-08-21 17:16:53 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：3094f656-f6f2-e9d4-3163-7536efaf9630
2025-08-21 17:16:53 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 17:17:21 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:18:10 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 3d7a1698-f355-de75-c492-c501c112b47e
2025-08-21 17:18:10 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:18:30 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 3d7a1698-f355-de75-c492-c501c112b47e
2025-08-21 17:18:30 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：3d7a1698-f355-de75-c492-c501c112b47e
2025-08-21 17:18:54 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:20:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8ec32e7b-b196-4e20-0148-ff182ec2acec
2025-08-21 17:20:04 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:20:17 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8ec32e7b-b196-4e20-0148-ff182ec2acec
2025-08-21 17:20:17 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8ec32e7b-b196-4e20-0148-ff182ec2acec
2025-08-21 17:20:17 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 17:20:49 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:22:08 | INFO     | app.core.state_manager:create_task:90 - 创建任务: be0c24ff-3296-9e87-4039-ceee25107fb5
2025-08-21 17:22:08 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:22:21 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: be0c24ff-3296-9e87-4039-ceee25107fb5
2025-08-21 17:22:21 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：be0c24ff-3296-9e87-4039-ceee25107fb5
2025-08-21 17:22:50 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:25:20 | INFO     | app.core.state_manager:create_task:90 - 创建任务: dba7af1f-4625-d9da-2c08-1001c2e7d5a0
2025-08-21 17:25:20 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:25:34 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: dba7af1f-4625-d9da-2c08-1001c2e7d5a0
2025-08-21 17:25:34 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：dba7af1f-4625-d9da-2c08-1001c2e7d5a0
2025-08-21 17:25:47 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:48:27 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 152ee376-100b-db18-3160-570d37354e36
2025-08-21 17:48:27 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:48:43 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 152ee376-100b-db18-3160-570d37354e36
2025-08-21 17:48:43 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：152ee376-100b-db18-3160-570d37354e36
2025-08-21 17:48:57 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:50:14 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c7502c01-8392-a7bb-951d-c37287f9d297
2025-08-21 17:50:14 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:50:38 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c7502c01-8392-a7bb-951d-c37287f9d297
2025-08-21 17:50:38 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c7502c01-8392-a7bb-951d-c37287f9d297
2025-08-21 17:51:50 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:55:57 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 44752a0c-8cc7-c274-20fe-65af9a07c604
2025-08-21 17:55:57 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:56:09 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 44752a0c-8cc7-c274-20fe-65af9a07c604
2025-08-21 17:56:09 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：44752a0c-8cc7-c274-20fe-65af9a07c604
2025-08-21 17:56:30 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:57:42 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5e35cea9-4d20-fd6e-284b-b87040ee6e10
2025-08-21 17:57:42 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 17:57:56 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 5e35cea9-4d20-fd6e-284b-b87040ee6e10
2025-08-21 17:57:56 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：5e35cea9-4d20-fd6e-284b-b87040ee6e10
2025-08-21 17:57:56 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 17:58:31 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 17:58:41 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-21 17:58:41 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-21 18:03:09 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 38983c9c-1478-c700-84c7-89bc79256181
2025-08-21 18:03:09 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:03:09 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 18:03:31 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 38983c9c-1478-c700-84c7-89bc79256181
2025-08-21 18:03:31 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：38983c9c-1478-c700-84c7-89bc79256181
2025-08-21 18:05:18 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:10:00 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 945727bc-203a-6e12-cd2d-a24411fe7745
2025-08-21 18:10:00 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:10:26 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 945727bc-203a-6e12-cd2d-a24411fe7745
2025-08-21 18:10:26 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：945727bc-203a-6e12-cd2d-a24411fe7745
2025-08-21 18:11:55 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:14:18 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 05860a35-a35c-8a0e-711e-be32cb7dd7e8
2025-08-21 18:14:18 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:14:33 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 05860a35-a35c-8a0e-711e-be32cb7dd7e8
2025-08-21 18:14:33 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：05860a35-a35c-8a0e-711e-be32cb7dd7e8
2025-08-21 18:14:59 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:15:08 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-21 18:15:08 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-21 18:15:48 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 83364c05-ed3a-8f26-2967-97f5ace1058c
2025-08-21 18:15:48 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:15:48 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 18:16:03 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 83364c05-ed3a-8f26-2967-97f5ace1058c
2025-08-21 18:16:03 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：83364c05-ed3a-8f26-2967-97f5ace1058c
2025-08-21 18:16:03 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 18:16:34 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:17:21 | INFO     | app.core.state_manager:create_task:90 - 创建任务: fad8bc16-89f9-a967-04f2-211d9885c2ed
2025-08-21 18:17:21 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:17:46 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: fad8bc16-89f9-a967-04f2-211d9885c2ed
2025-08-21 18:17:46 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：fad8bc16-89f9-a967-04f2-211d9885c2ed
2025-08-21 18:17:46 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 18:18:58 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:19:39 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a0b57375-6c59-5e66-b5f5-22be8ba810f1
2025-08-21 18:19:39 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:20:01 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a0b57375-6c59-5e66-b5f5-22be8ba810f1
2025-08-21 18:20:01 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a0b57375-6c59-5e66-b5f5-22be8ba810f1
2025-08-21 18:21:58 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:22:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9236e134-e8a5-8355-c204-b87f53a2bf7f
2025-08-21 18:22:49 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:23:07 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 9236e134-e8a5-8355-c204-b87f53a2bf7f
2025-08-21 18:23:07 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：9236e134-e8a5-8355-c204-b87f53a2bf7f
2025-08-21 18:23:59 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:25:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cfd24a80-053c-fa21-efca-27742cdca568
2025-08-21 18:25:12 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:25:32 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: cfd24a80-053c-fa21-efca-27742cdca568
2025-08-21 18:25:32 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：cfd24a80-053c-fa21-efca-27742cdca568
2025-08-21 18:25:57 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:26:17 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-21 18:26:17 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-21 18:26:56 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b87155af-7a43-46dd-7290-02bf284640d4
2025-08-21 18:26:56 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:26:56 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 18:27:20 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b87155af-7a43-46dd-7290-02bf284640d4
2025-08-21 18:27:20 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b87155af-7a43-46dd-7290-02bf284640d4
2025-08-21 18:28:17 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:28:44 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 61485830-5eba-82a2-b5b0-64bad584c793
2025-08-21 18:28:44 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:29:08 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 61485830-5eba-82a2-b5b0-64bad584c793
2025-08-21 18:29:08 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：61485830-5eba-82a2-b5b0-64bad584c793
2025-08-21 18:30:02 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:30:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7f56e2d1-7c86-af68-5177-09631037d391
2025-08-21 18:30:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:31:05 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 7f56e2d1-7c86-af68-5177-09631037d391
2025-08-21 18:31:05 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：7f56e2d1-7c86-af68-5177-09631037d391
2025-08-21 18:31:19 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:33:21 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c1e9bead-198c-414d-1a2e-e22e27559fa8
2025-08-21 18:33:21 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:33:35 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c1e9bead-198c-414d-1a2e-e22e27559fa8
2025-08-21 18:33:35 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c1e9bead-198c-414d-1a2e-e22e27559fa8
2025-08-21 18:33:52 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:34:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 09b2545e-6e73-ff88-2f57-acfcf6ca3c18
2025-08-21 18:34:59 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:35:30 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 09b2545e-6e73-ff88-2f57-acfcf6ca3c18
2025-08-21 18:35:30 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：09b2545e-6e73-ff88-2f57-acfcf6ca3c18
2025-08-21 18:36:24 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:37:25 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b22accb7-73c7-9cca-c57f-fe36183d74e5
2025-08-21 18:37:25 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:37:47 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b22accb7-73c7-9cca-c57f-fe36183d74e5
2025-08-21 18:37:47 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b22accb7-73c7-9cca-c57f-fe36183d74e5
2025-08-21 18:38:36 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:40:19 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9c4a43e6-31ec-72e4-7d55-056cd7cf9770
2025-08-21 18:40:19 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:40:42 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 9c4a43e6-31ec-72e4-7d55-056cd7cf9770
2025-08-21 18:40:42 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：9c4a43e6-31ec-72e4-7d55-056cd7cf9770
2025-08-21 18:40:42 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 18:41:54 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:42:34 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d33e7480-5ed5-b996-d1e3-0a9f4e66b0f1
2025-08-21 18:42:34 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:42:52 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: d33e7480-5ed5-b996-d1e3-0a9f4e66b0f1
2025-08-21 18:42:52 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：d33e7480-5ed5-b996-d1e3-0a9f4e66b0f1
2025-08-21 18:44:11 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:44:41 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-21 18:44:41 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-21 18:45:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b44ac583-293e-bfb5-b1e2-f996b5ad7474
2025-08-21 18:45:06 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:45:06 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 18:45:26 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b44ac583-293e-bfb5-b1e2-f996b5ad7474
2025-08-21 18:45:26 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b44ac583-293e-bfb5-b1e2-f996b5ad7474
2025-08-21 18:46:24 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:47:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1363199f-9439-4bb3-1826-2767e1784762
2025-08-21 18:47:12 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:47:33 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 1363199f-9439-4bb3-1826-2767e1784762
2025-08-21 18:47:33 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：1363199f-9439-4bb3-1826-2767e1784762
2025-08-21 18:48:22 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:49:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f69024e0-145d-e1d5-d3ed-38f7cc6d03aa
2025-08-21 18:49:04 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:49:25 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f69024e0-145d-e1d5-d3ed-38f7cc6d03aa
2025-08-21 18:49:25 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f69024e0-145d-e1d5-d3ed-38f7cc6d03aa
2025-08-21 18:49:25 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-21 18:50:08 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:50:46 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ebdd588c-ecfe-985b-1442-c4a00d728910
2025-08-21 18:50:46 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:51:06 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: ebdd588c-ecfe-985b-1442-c4a00d728910
2025-08-21 18:51:06 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：ebdd588c-ecfe-985b-1442-c4a00d728910
2025-08-21 18:51:31 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:52:33 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 88b0bb4b-cc9d-09ea-33ad-6d515f0237b7
2025-08-21 18:52:33 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:52:58 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 88b0bb4b-cc9d-09ea-33ad-6d515f0237b7
2025-08-21 18:52:58 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：88b0bb4b-cc9d-09ea-33ad-6d515f0237b7
2025-08-21 18:54:15 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:54:55 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4640a738-4d21-78e5-6ffb-cf7dc58fec73
2025-08-21 18:54:55 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:55:14 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 4640a738-4d21-78e5-6ffb-cf7dc58fec73
2025-08-21 18:55:14 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：4640a738-4d21-78e5-6ffb-cf7dc58fec73
2025-08-21 18:55:40 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:56:33 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 257311de-d826-04da-4cb8-d13ce4d53fac
2025-08-21 18:56:33 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:56:59 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 257311de-d826-04da-4cb8-d13ce4d53fac
2025-08-21 18:56:59 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：257311de-d826-04da-4cb8-d13ce4d53fac
2025-08-21 18:57:25 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 18:59:00 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e1b23922-6638-e800-680f-189653338afe
2025-08-21 18:59:00 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-21 18:59:20 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: e1b23922-6638-e800-680f-189653338afe
2025-08-21 18:59:20 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：e1b23922-6638-e800-680f-189653338afe
2025-08-21 19:00:03 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-21 19:01:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9ead4538-39dc-8dc0-af96-3d5e6b599e25
2025-08-21 19:01:04 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:01:04 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:01:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> processing (进度: 0%)
2025-08-21 19:01:24 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:01:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9ead4538-39dc-8dc0-af96-3d5e6b599e25 -> completed (进度: 100%)
2025-08-21 19:02:24 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bb028b76-017f-a5a0-fe78-84ac47a11566
2025-08-21 19:02:24 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:02:24 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:02:24 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-21 19:02:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 0%)
2025-08-21 19:02:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:02:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 80%)
2025-08-21 19:03:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 62%)
2025-08-21 19:03:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:03:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> processing (进度: 56%)
2025-08-21 19:04:06 | WARNING  | app.services.question_cloner:clone_single_child:406 - 子题目3LLM生成失败，保持原内容
2025-08-21 19:04:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:04:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bb028b76-017f-a5a0-fe78-84ac47a11566 -> completed (进度: 100%)
2025-08-21 19:04:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 3b3ee692-0594-f8dc-94ea-cd0d400a861d
2025-08-21 19:04:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:04:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:04:57 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e3835057-f907-e4ee-c613-3d7b2a435b5a
2025-08-21 19:04:57 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:04:57 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:04:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:04:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> processing (进度: 0%)
2025-08-21 19:05:15 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:05:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e3835057-f907-e4ee-c613-3d7b2a435b5a -> completed (进度: 100%)
2025-08-21 19:05:25 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d4f65b40-1035-1c4b-20d5-c673cf61f65a
2025-08-21 19:05:25 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:05:25 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:05:25 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:05:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> processing (进度: 0%)
2025-08-21 19:05:48 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:05:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4f65b40-1035-1c4b-20d5-c673cf61f65a -> completed (进度: 100%)
2025-08-21 19:06:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09
2025-08-21 19:06:12 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:06:12 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:06:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> processing (进度: 0%)
2025-08-21 19:06:33 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:06:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9dd71e2b-23e3-4918-d0d6-2c7b3e5e3c09 -> completed (进度: 100%)
2025-08-21 19:06:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f889d39f-45c6-ad46-f4d1-7d379880ed78
2025-08-21 19:06:53 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-21 19:06:53 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-21 19:06:53 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-21 19:06:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:06:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:06:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:06:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:06:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:06:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 0%)
2025-08-21 19:07:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 80%)
2025-08-21 19:07:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 62%)
2025-08-21 19:07:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 62%)
2025-08-21 19:07:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 68%)
2025-08-21 19:07:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 74%)
2025-08-21 19:07:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 74%)
2025-08-21 19:07:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 74%)
2025-08-21 19:07:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> processing (进度: 56%)
2025-08-21 19:07:42 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-21 19:07:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f889d39f-45c6-ad46-f4d1-7d379880ed78 -> completed (进度: 100%)
