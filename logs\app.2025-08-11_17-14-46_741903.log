2025-08-11 17:14:46 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e4d85a14-7eab-8045-5042-7a92bbde5964
2025-08-11 17:14:46 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:14:46 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-11 17:15:46 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: e4d85a14-7eab-8045-5042-7a92bbde5964
2025-08-11 17:15:46 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：e4d85a14-7eab-8045-5042-7a92bbde5964
2025-08-11 17:16:06 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:17:03 | INFO     | app.core.state_manager:create_task:90 - 创建任务: db74a49f-1347-72c3-773b-2ac2281a8352
2025-08-11 17:17:03 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:18:10 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: db74a49f-1347-72c3-773b-2ac2281a8352
2025-08-11 17:18:10 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：db74a49f-1347-72c3-773b-2ac2281a8352
2025-08-11 17:19:01 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:19:46 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cfb90c22-32a2-e6f5-c8cc-29a1f890582e
2025-08-11 17:19:46 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:20:18 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: cfb90c22-32a2-e6f5-c8cc-29a1f890582e
2025-08-11 17:20:18 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：cfb90c22-32a2-e6f5-c8cc-29a1f890582e
2025-08-11 17:21:09 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:23:07 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 0db23fc0-b723-d6e9-ee29-7a9f6f72a2d2
2025-08-11 17:23:07 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:24:43 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 0db23fc0-b723-d6e9-ee29-7a9f6f72a2d2
2025-08-11 17:24:43 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：0db23fc0-b723-d6e9-ee29-7a9f6f72a2d2
2025-08-11 17:26:31 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:30:11 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bea0b26d-f42d-126f-e29a-d8b779bd9f1a
2025-08-11 17:30:11 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:31:13 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: bea0b26d-f42d-126f-e29a-d8b779bd9f1a
2025-08-11 17:31:13 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：bea0b26d-f42d-126f-e29a-d8b779bd9f1a
2025-08-11 17:31:53 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-11 17:33:56 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:36:13 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5b80b9be-6af8-d72c-d15e-19ca6e7fa319
2025-08-11 17:36:13 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:37:14 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 5b80b9be-6af8-d72c-d15e-19ca6e7fa319
2025-08-11 17:37:14 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：5b80b9be-6af8-d72c-d15e-19ca6e7fa319
2025-08-11 17:38:01 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:41:11 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:41:11 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:42:14 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:42:14 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:43:02 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-11 17:44:56 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:56 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:56 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:56 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:57 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:58 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:44:59 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 206d5c60-f5ab-fbd6-4819-ca446f3aeec3
2025-08-11 17:46:43 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:48:57 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6297e0b3-3f7d-e7e0-f6c4-74ce77d51d5d
2025-08-11 17:48:57 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 17:49:24 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 6297e0b3-3f7d-e7e0-f6c4-74ce77d51d5d
2025-08-11 17:49:24 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：6297e0b3-3f7d-e7e0-f6c4-74ce77d51d5d
2025-08-11 17:50:14 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 17:54:26 | INFO     | app.core.state_manager:create_task:90 - 创建任务: dc690826-b58a-c485-23f9-1fc160732f4b
2025-08-11 17:54:26 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-11 17:54:26 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-11 17:54:26 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 728529ee-7ad5-ddbd-8114-f2a95413e995
2025-08-11 17:54:26 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-11 17:54:26 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-11 17:54:26 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 193e4b32-1c04-0b09-4015-c13f35a12672
2025-08-11 17:54:26 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-11 17:54:26 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-11 17:54:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> processing (进度: 0%)
2025-08-11 17:54:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:35 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-11 17:54:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: dc690826-b58a-c485-23f9-1fc160732f4b -> completed (进度: 100%)
2025-08-11 17:54:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> processing (进度: 0%)
2025-08-11 17:54:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:38 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-11 17:54:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 728529ee-7ad5-ddbd-8114-f2a95413e995 -> completed (进度: 100%)
2025-08-11 17:54:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> processing (进度: 0%)
2025-08-11 17:54:41 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-11 17:54:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 193e4b32-1c04-0b09-4015-c13f35a12672 -> completed (进度: 100%)
