2025-08-01 10:18:55 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 06a290c5-3a37-002c-9174-00bf05b6646a
2025-08-01 10:18:55 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 10:19:15 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 10:20:14 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b08dcfdc-1182-02c4-9516-3bfd2da211af
2025-08-01 10:20:14 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 10:20:36 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 10:23:10 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 038b4354-4c1a-81c6-530a-d65e033c893c
2025-08-01 10:23:10 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 10:23:21 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 038b4354-4c1a-81c6-530a-d65e033c893c
2025-08-01 10:23:21 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：038b4354-4c1a-81c6-530a-d65e033c893c
2025-08-01 10:23:34 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:33:07 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7c2dafe9-c19d-c085-b75d-8e0670f028e7
2025-08-01 11:33:07 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:33:09 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:33:09 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:33:09 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:33:09 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Connection error.
2025-08-01 11:33:09 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 7c2dafe9-c19d-c085-b75d-8e0670f028e7
2025-08-01 11:33:09 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：7c2dafe9-c19d-c085-b75d-8e0670f028e7
2025-08-01 11:33:11 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:33:11 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:33:11 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:33:11 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:33:13 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:33:13 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:33:14 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:33:16 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Connection error.
2025-08-01 11:33:18 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:33:19 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Connection error.
2025-08-01 11:33:21 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:33:22 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:33:22 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:33:22 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:34:01 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bf1de5a1-783c-ab28-7e7c-4495ce5b3a6e
2025-08-01 11:34:01 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:34:02 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:34:02 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:34:02 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:34:02 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Connection error.
2025-08-01 11:34:02 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: bf1de5a1-783c-ab28-7e7c-4495ce5b3a6e
2025-08-01 11:34:02 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：bf1de5a1-783c-ab28-7e7c-4495ce5b3a6e
2025-08-01 11:34:04 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:34:04 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:34:04 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:34:04 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:34:07 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:34:07 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:34:08 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:34:09 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Connection error.
2025-08-01 11:34:11 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:34:12 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Connection error.
2025-08-01 11:34:14 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:34:16 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:34:16 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:34:16 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:39:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4db8fe46-681d-8795-3c02-fd74ad79fbc3
2025-08-01 11:39:12 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:39:13 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:13 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:13 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:13 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Connection error.
2025-08-01 11:39:13 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 4db8fe46-681d-8795-3c02-fd74ad79fbc3
2025-08-01 11:39:13 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：4db8fe46-681d-8795-3c02-fd74ad79fbc3
2025-08-01 11:39:15 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:15 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:15 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:15 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:17 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:39:17 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:39:19 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:20 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Connection error.
2025-08-01 11:39:22 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:23 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Connection error.
2025-08-01 11:39:25 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:27 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:39:27 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:39:27 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:39:46 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b2efb013-6aed-84ff-20d0-c1d3316d4c04
2025-08-01 11:39:46 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:39:48 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:48 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:48 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:48 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Connection error.
2025-08-01 11:39:48 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b2efb013-6aed-84ff-20d0-c1d3316d4c04
2025-08-01 11:39:48 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b2efb013-6aed-84ff-20d0-c1d3316d4c04
2025-08-01 11:39:50 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:50 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:50 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:50 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:52 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:39:52 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:39:54 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:55 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Connection error.
2025-08-01 11:39:57 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:58 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Connection error.
2025-08-01 11:40:00 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:40:02 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:40:02 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:40:02 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:42:38 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bab15073-8da9-4b8b-2521-befa716a4ec7
2025-08-01 11:42:38 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:43:11 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: bab15073-8da9-4b8b-2521-befa716a4ec7
2025-08-01 11:43:11 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：bab15073-8da9-4b8b-2521-befa716a4ec7
2025-08-01 11:43:48 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:44:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a9e767c5-c2df-c3d1-823b-0b2f352173e0
2025-08-01 11:44:43 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a9e767c5-c2df-c3d1-823b-0b2f352173e0
2025-08-01 11:44:44 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a9e767c5-c2df-c3d1-823b-0b2f352173e0
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:44:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:46:10 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:50:42 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8e67b843-a8d9-fe33-bb1a-946ab1123947
2025-08-01 11:50:42 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 11:51:22 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8e67b843-a8d9-fe33-bb1a-946ab1123947
2025-08-01 11:51:22 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8e67b843-a8d9-fe33-bb1a-946ab1123947
2025-08-01 11:51:22 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-01 11:52:17 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: 解析结果验证失败
2025-08-01 11:53:02 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: 解析结果验证失败
2025-08-01 11:53:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:53:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: 解析结果验证失败
2025-08-01 11:54:03 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 11:57:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6096e454-3297-9e67-5bb5-14147c249167
2025-08-01 11:57:12 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 11:57:12 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 11:57:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> processing (进度: 0%)
2025-08-01 11:57:51 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 11:57:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6096e454-3297-9e67-5bb5-14147c249167 -> completed (进度: 100%)
2025-08-01 12:11:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4dd5363c-7144-2414-47e3-361204d2e64b
2025-08-01 12:11:35 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 12:11:35 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 12:11:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:11:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> processing (进度: 0%)
2025-08-01 12:12:08 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 12:12:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4dd5363c-7144-2414-47e3-361204d2e64b -> completed (进度: 100%)
2025-08-01 12:12:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f9347cdb-0dcc-da7a-b239-e82b12645c9c
2025-08-01 12:12:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 12:12:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 12:12:59 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 12:13:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 0%)
2025-08-01 12:13:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:13:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 80%)
2025-08-01 12:14:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 60%)
2025-08-01 12:14:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 60%)
2025-08-01 12:14:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 60%)
2025-08-01 12:14:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 70%)
2025-08-01 12:14:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 70%)
2025-08-01 12:14:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 70%)
2025-08-01 12:14:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 70%)
2025-08-01 12:14:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 70%)
2025-08-01 12:14:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> processing (进度: 70%)
2025-08-01 12:14:31 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 12:14:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9347cdb-0dcc-da7a-b239-e82b12645c9c -> completed (进度: 100%)
2025-08-01 12:16:11 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8c8b3086-f7ed-2c5c-485b-9f2a6ba12f8e
2025-08-01 12:16:11 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 12:17:07 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8c8b3086-f7ed-2c5c-485b-9f2a6ba12f8e
2025-08-01 12:17:07 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8c8b3086-f7ed-2c5c-485b-9f2a6ba12f8e
2025-08-01 12:17:07 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-01 12:19:44 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 13:45:21 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 87a4b8f1-ee86-8161-27f5-591216dcbb44
2025-08-01 13:45:21 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 13:45:21 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 13:45:21 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 13:45:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 0%)
2025-08-01 13:45:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:45:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 56%)
2025-08-01 13:45:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 56%)
2025-08-01 13:45:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 74%)
2025-08-01 13:45:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 68%)
2025-08-01 13:45:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> processing (进度: 80%)
2025-08-01 13:46:10 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 13:46:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 87a4b8f1-ee86-8161-27f5-591216dcbb44 -> completed (进度: 100%)
2025-08-01 13:47:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a068bfa7-fb5a-469f-93b7-71ee1592846b
2025-08-01 13:47:12 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 13:47:35 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a068bfa7-fb5a-469f-93b7-71ee1592846b
2025-08-01 13:47:35 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a068bfa7-fb5a-469f-93b7-71ee1592846b
2025-08-01 13:48:13 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 13:50:41 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 13:50:41 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 13:51:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655
2025-08-01 13:51:31 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 13:51:31 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 13:51:31 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 13:51:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 0%)
2025-08-01 13:51:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 80%)
2025-08-01 13:51:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 56%)
2025-08-01 13:51:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 68%)
2025-08-01 13:52:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 62%)
2025-08-01 13:52:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 74%)
2025-08-01 13:52:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 74%)
2025-08-01 13:52:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 74%)
2025-08-01 13:52:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 74%)
2025-08-01 13:52:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 74%)
2025-08-01 13:52:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> processing (进度: 74%)
2025-08-01 13:52:25 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 13:52:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9d6f15ea-1ed2-4b4e-dcd7-ae975c6f8655 -> completed (进度: 100%)
2025-08-01 13:55:55 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 13:55:55 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 13:56:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa
2025-08-01 13:56:54 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 13:56:54 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 13:56:54 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 13:56:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:56:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:56:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:56:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:56:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:56:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 0%)
2025-08-01 13:57:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 62%)
2025-08-01 13:57:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 62%)
2025-08-01 13:57:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 56%)
2025-08-01 13:57:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 74%)
2025-08-01 13:57:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:57:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:58:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:58:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:58:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> processing (进度: 80%)
2025-08-01 13:58:03 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 13:58:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 580d45d2-2c1f-8e42-3632-7d6ee5e01faa -> completed (进度: 100%)
2025-08-01 13:58:15 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4ea1de2c-8853-83ab-9d11-91043da0fe6b
2025-08-01 13:58:15 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 13:58:15 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 13:58:15 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 13:58:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 0%)
2025-08-01 13:58:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 80%)
2025-08-01 13:58:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 74%)
2025-08-01 13:58:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 74%)
2025-08-01 13:58:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 56%)
2025-08-01 13:58:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 56%)
2025-08-01 13:58:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 68%)
2025-08-01 13:58:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> processing (进度: 68%)
2025-08-01 13:58:55 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 13:58:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4ea1de2c-8853-83ab-9d11-91043da0fe6b -> completed (进度: 100%)
2025-08-01 14:08:14 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 14:08:14 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 14:09:10 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d107a0b9-a1ff-7939-9160-e23de18f541a
2025-08-01 14:09:10 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:09:10 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:09:10 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 14:09:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 0%)
2025-08-01 14:09:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 80%)
2025-08-01 14:09:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 62%)
2025-08-01 14:09:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 68%)
2025-08-01 14:09:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:09:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> processing (进度: 74%)
2025-08-01 14:10:05 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:10:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d107a0b9-a1ff-7939-9160-e23de18f541a -> completed (进度: 100%)
2025-08-01 14:16:16 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f2474e3b-4adb-62d0-8bfc-95316b50fd8a
2025-08-01 14:16:16 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:16:16 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:16:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> processing (进度: 0%)
2025-08-01 14:16:32 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:16:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f2474e3b-4adb-62d0-8bfc-95316b50fd8a -> completed (进度: 100%)
2025-08-01 14:20:01 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 14:20:01 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 14:20:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7bff6370-fe6f-196b-dc65-472530f0c698
2025-08-01 14:20:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:20:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:20:59 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 14:21:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 0%)
2025-08-01 14:21:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 80%)
2025-08-01 14:21:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 68%)
2025-08-01 14:21:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:21:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 56%)
2025-08-01 14:22:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 74%)
2025-08-01 14:22:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 74%)
2025-08-01 14:22:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 74%)
2025-08-01 14:22:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> processing (进度: 74%)
2025-08-01 14:22:10 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:22:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7bff6370-fe6f-196b-dc65-472530f0c698 -> completed (进度: 100%)
2025-08-01 14:27:18 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 14:27:18 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 14:29:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7cda394c-90e8-4c28-30a9-3b616bbb6286
2025-08-01 14:29:31 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:29:31 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:29:31 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 14:29:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 0%)
2025-08-01 14:29:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:29:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:30:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:30:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 56%)
2025-08-01 14:30:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 56%)
2025-08-01 14:30:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:30:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:30:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 80%)
2025-08-01 14:30:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 74%)
2025-08-01 14:30:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 74%)
2025-08-01 14:30:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 74%)
2025-08-01 14:30:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 74%)
2025-08-01 14:30:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 68%)
2025-08-01 14:30:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 68%)
2025-08-01 14:30:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 68%)
2025-08-01 14:30:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 68%)
2025-08-01 14:30:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> processing (进度: 68%)
2025-08-01 14:30:25 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:30:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7cda394c-90e8-4c28-30a9-3b616bbb6286 -> completed (进度: 100%)
2025-08-01 14:32:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cbd77208-7ad9-5071-8906-7202823d8220
2025-08-01 14:32:53 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:32:53 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:32:53 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 14:32:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:32:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:32:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:32:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:32:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:32:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 0%)
2025-08-01 14:33:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 80%)
2025-08-01 14:33:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 68%)
2025-08-01 14:33:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 68%)
2025-08-01 14:33:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 74%)
2025-08-01 14:33:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 74%)
2025-08-01 14:33:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 74%)
2025-08-01 14:33:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 74%)
2025-08-01 14:33:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 56%)
2025-08-01 14:33:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 56%)
2025-08-01 14:33:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 56%)
2025-08-01 14:33:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 56%)
2025-08-01 14:33:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> processing (进度: 56%)
2025-08-01 14:33:41 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:33:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cbd77208-7ad9-5071-8906-7202823d8220 -> completed (进度: 100%)
2025-08-01 14:35:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 99be705f-4af4-cbdf-d7bd-601095e258fb
2025-08-01 14:35:04 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:35:04 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:35:04 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 14:35:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 0%)
2025-08-01 14:35:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 80%)
2025-08-01 14:35:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 62%)
2025-08-01 14:35:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 68%)
2025-08-01 14:35:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> processing (进度: 68%)
2025-08-01 14:35:34 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:35:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 99be705f-4af4-cbdf-d7bd-601095e258fb -> completed (进度: 100%)
2025-08-01 14:37:17 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5e9e2ede-b803-b8ae-a8cf-4a86e82e54a0
2025-08-01 14:37:17 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 14:37:17 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:37:37 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 5e9e2ede-b803-b8ae-a8cf-4a86e82e54a0
2025-08-01 14:37:37 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：5e9e2ede-b803-b8ae-a8cf-4a86e82e54a0
2025-08-01 14:37:37 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-01 14:38:38 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 14:39:11 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 36b84315-82ea-7564-4ae3-912bd6790a0d
2025-08-01 14:39:11 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 14:39:34 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 36b84315-82ea-7564-4ae3-912bd6790a0d
2025-08-01 14:39:34 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：36b84315-82ea-7564-4ae3-912bd6790a0d
2025-08-01 14:40:12 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 14:50:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2c567c65-3e1c-19c5-0951-c50023fc6a9f
2025-08-01 14:50:54 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 14:51:06 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 2c567c65-3e1c-19c5-0951-c50023fc6a9f
2025-08-01 14:51:06 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：2c567c65-3e1c-19c5-0951-c50023fc6a9f
2025-08-01 14:51:22 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 14:51:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b4387eb9-f32f-2773-0fe7-dbd388e4f6d5
2025-08-01 14:51:53 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 14:52:11 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b4387eb9-f32f-2773-0fe7-dbd388e4f6d5
2025-08-01 14:52:11 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b4387eb9-f32f-2773-0fe7-dbd388e4f6d5
2025-08-01 14:52:26 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 14:53:26 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5f486696-1917-81a3-1fbf-03270bc7fb70
2025-08-01 14:53:26 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:53:26 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:53:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> processing (进度: 0%)
2025-08-01 14:53:39 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:53:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5f486696-1917-81a3-1fbf-03270bc7fb70 -> completed (进度: 100%)
2025-08-01 14:53:56 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 032a1931-3229-b9f5-ccb3-eef0ae32b000
2025-08-01 14:53:56 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 14:53:56 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 14:53:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:53:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:53:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:53:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> processing (进度: 0%)
2025-08-01 14:54:10 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 14:54:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 032a1931-3229-b9f5-ccb3-eef0ae32b000 -> completed (进度: 100%)
2025-08-01 15:53:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 36a2a680-2136-a83d-4dd0-06a4680e6b7f
2025-08-01 15:53:54 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:53:54 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:53:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 184e07ab-ffab-a4b3-5959-af34b4a50bac
2025-08-01 15:53:54 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:53:54 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:53:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d257ca8d-a124-4878-813b-1a14a24d2c5a
2025-08-01 15:53:54 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:53:54 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:53:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b96aa54d-8b6f-15e8-1908-de6b319491bc
2025-08-01 15:53:54 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:53:54 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:53:54 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f
2025-08-01 15:53:54 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:53:54 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:53:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:53:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:53:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:53:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:53:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:53:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:53:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:53:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:53:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:53:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:53:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:53:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:53:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:53:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:53:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:53:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:53:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:53:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:53:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:53:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:53:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:53:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:53:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:53:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:53:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> processing (进度: 0%)
2025-08-01 15:54:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:10 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:54:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b96aa54d-8b6f-15e8-1908-de6b319491bc -> completed (进度: 100%)
2025-08-01 15:54:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> processing (进度: 0%)
2025-08-01 15:54:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> processing (进度: 0%)
2025-08-01 15:54:12 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:54:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> processing (进度: 0%)
2025-08-01 15:54:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d257ca8d-a124-4878-813b-1a14a24d2c5a -> completed (进度: 100%)
2025-08-01 15:54:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> processing (进度: 0%)
2025-08-01 15:54:12 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:54:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 36a2a680-2136-a83d-4dd0-06a4680e6b7f -> completed (进度: 100%)
2025-08-01 15:54:13 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:54:13 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:54:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e2747e8d-0ede-7b4e-3814-bc1dabc7b93f -> completed (进度: 100%)
2025-08-01 15:54:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 184e07ab-ffab-a4b3-5959-af34b4a50bac -> completed (进度: 100%)
2025-08-01 15:54:40 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b1669332-ef3b-43f0-cae7-863e07ad5898
2025-08-01 15:54:40 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:54:40 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:54:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> processing (进度: 0%)
2025-08-01 15:54:51 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:54:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b1669332-ef3b-43f0-cae7-863e07ad5898 -> completed (进度: 100%)
2025-08-01 15:55:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4550a9af-e896-4911-d452-781f76fe567b
2025-08-01 15:55:04 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:55:04 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:55:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5d4d47a4-74e3-e375-db60-766cb309fa7f
2025-08-01 15:55:04 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:55:04 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:55:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ac598989-9438-e685-2b86-4eee58153929
2025-08-01 15:55:04 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:55:04 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:55:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> processing (进度: 0%)
2025-08-01 15:55:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:20 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:55:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5d4d47a4-74e3-e375-db60-766cb309fa7f -> completed (进度: 100%)
2025-08-01 15:55:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> processing (进度: 0%)
2025-08-01 15:55:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> processing (进度: 0%)
2025-08-01 15:55:22 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:55:22 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:55:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ac598989-9438-e685-2b86-4eee58153929 -> completed (进度: 100%)
2025-08-01 15:55:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4550a9af-e896-4911-d452-781f76fe567b -> completed (进度: 100%)
2025-08-01 15:56:44 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7
2025-08-01 15:56:44 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:56:44 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:56:44 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 15:56:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:56:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:57:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:57:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:57:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:57:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 0%)
2025-08-01 15:57:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 56%)
2025-08-01 15:57:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 62%)
2025-08-01 15:57:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 62%)
2025-08-01 15:57:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 62%)
2025-08-01 15:57:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 62%)
2025-08-01 15:57:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 62%)
2025-08-01 15:57:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 62%)
2025-08-01 15:57:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 68%)
2025-08-01 15:57:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> processing (进度: 80%)
2025-08-01 15:57:27 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:57:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8bd56cba-4275-5fe6-4ddd-2ce265d0caa7 -> completed (进度: 100%)
2025-08-01 15:57:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 000cc924-d55f-425a-50d1-d864a7a43d41
2025-08-01 15:57:53 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:57:53 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:57:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cd01bf1f-3309-f957-173f-a255ba7b1463
2025-08-01 15:57:53 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 15:57:53 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 15:57:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:57:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:57:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:57:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:57:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:57:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:57:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:57:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:57:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:57:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:57:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:57:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> processing (进度: 0%)
2025-08-01 15:58:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:58:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 000cc924-d55f-425a-50d1-d864a7a43d41 -> completed (进度: 100%)
2025-08-01 15:58:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> processing (进度: 0%)
2025-08-01 15:58:14 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 15:58:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: cd01bf1f-3309-f957-173f-a255ba7b1463 -> completed (进度: 100%)
2025-08-01 16:01:25 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4
2025-08-01 16:01:25 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:01:25 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:01:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> processing (进度: 0%)
2025-08-01 16:01:52 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:01:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8cf63a46-fc98-88e7-2a3c-bf33a04815d4 -> completed (进度: 100%)
2025-08-01 16:10:47 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 16:10:47 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 16:11:39 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c19ada94-e78c-19fd-9509-9c0f3aa67626
2025-08-01 16:11:39 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:11:39 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:11:39 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7
2025-08-01 16:11:39 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:11:39 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:11:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> processing (进度: 0%)
2025-08-01 16:11:49 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:11:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: c19ada94-e78c-19fd-9509-9c0f3aa67626 -> completed (进度: 100%)
2025-08-01 16:11:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> processing (进度: 0%)
2025-08-01 16:11:50 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:11:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 485b2e9c-92e4-0e7c-f97e-d3ac9e3dadb7 -> completed (进度: 100%)
2025-08-01 16:12:02 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd
2025-08-01 16:12:02 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:12:02 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:12:02 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7555c518-1ccb-e4e7-82c0-7b1191dc4829
2025-08-01 16:12:02 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:12:02 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:12:02 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d
2025-08-01 16:12:02 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:12:02 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:12:02 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 25f337fb-9f12-3569-6ead-6011e27754f7
2025-08-01 16:12:02 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:12:02 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:12:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> processing (进度: 0%)
2025-08-01 16:12:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:14 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:12:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7e18eea3-806d-dfb4-d0af-a6fbb4d2d17d -> completed (进度: 100%)
2025-08-01 16:12:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> processing (进度: 0%)
2025-08-01 16:12:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:15 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:12:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 593e9c0a-a86c-0b45-a2d2-d6e5d7ba05cd -> completed (进度: 100%)
2025-08-01 16:12:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> processing (进度: 0%)
2025-08-01 16:12:16 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:12:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> processing (进度: 0%)
2025-08-01 16:12:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 25f337fb-9f12-3569-6ead-6011e27754f7 -> completed (进度: 100%)
2025-08-01 16:12:17 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:12:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7555c518-1ccb-e4e7-82c0-7b1191dc4829 -> completed (进度: 100%)
2025-08-01 16:15:27 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 16:15:27 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 16:15:36 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b9be48bb-a1da-8db4-1e35-942caea48d35
2025-08-01 16:15:36 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:15:36 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:15:36 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1800912d-705d-7bd3-33a4-f4b7db40e7f9
2025-08-01 16:15:36 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:15:36 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:15:36 | INFO     | app.core.state_manager:create_task:90 - 创建任务: fd7ce1c7-18ca-d254-22d0-4f705536007a
2025-08-01 16:15:36 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:15:36 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:15:37 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330
2025-08-01 16:15:37 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:15:37 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:15:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:15:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:15:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:15:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:15:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:16:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:16:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:16:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> processing (进度: 0%)
2025-08-01 16:16:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> processing (进度: 0%)
2025-08-01 16:16:02 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:16:03 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:16:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b9be48bb-a1da-8db4-1e35-942caea48d35 -> completed (进度: 100%)
2025-08-01 16:16:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3f37b6fa-3a51-6d97-a3b7-bd5f99593330 -> completed (进度: 100%)
2025-08-01 16:16:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> processing (进度: 0%)
2025-08-01 16:16:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:16:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1800912d-705d-7bd3-33a4-f4b7db40e7f9 -> completed (进度: 100%)
2025-08-01 16:16:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> processing (进度: 0%)
2025-08-01 16:16:37 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:16:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fd7ce1c7-18ca-d254-22d0-4f705536007a -> completed (进度: 100%)
2025-08-01 16:16:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2a14d407-288f-e166-38b0-123054b43083
2025-08-01 16:16:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:16:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:16:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4293ff83-c792-fc74-c6fd-f50ab9573eba
2025-08-01 16:16:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:16:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:16:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0
2025-08-01 16:16:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:16:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:16:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d4eee4de-57fe-3020-3f47-0c685b6bd9ce
2025-08-01 16:16:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:16:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:16:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bc1724c5-b700-d635-f4d8-533b3b530504
2025-08-01 16:16:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:16:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:17:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> processing (进度: 0%)
2025-08-01 16:17:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:21 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:17:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1379a1e2-c64d-a459-1e2d-dea99a7c6fd0 -> completed (进度: 100%)
2025-08-01 16:17:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> processing (进度: 0%)
2025-08-01 16:17:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> processing (进度: 0%)
2025-08-01 16:17:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:23 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:17:23 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:17:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d4eee4de-57fe-3020-3f47-0c685b6bd9ce -> completed (进度: 100%)
2025-08-01 16:17:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 4293ff83-c792-fc74-c6fd-f50ab9573eba -> completed (进度: 100%)
2025-08-01 16:17:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> processing (进度: 0%)
2025-08-01 16:17:48 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:17:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bc1724c5-b700-d635-f4d8-533b3b530504 -> completed (进度: 100%)
2025-08-01 16:17:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:17:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:18:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:18:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:18:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:18:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:18:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> processing (进度: 0%)
2025-08-01 16:18:05 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:18:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2a14d407-288f-e166-38b0-123054b43083 -> completed (进度: 100%)
2025-08-01 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 937df4b5-a3c2-82bf-e832-22b21c4b16b5
2025-08-01 16:18:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:30 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 3edc9373-38f8-76c7-8f6a-5ff6305de96a
2025-08-01 16:18:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:30 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6b4fe026-a213-691b-9f14-bf20e70e233a
2025-08-01 16:18:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:30 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e28da83c-43bc-7430-b9d3-44773718eaf1
2025-08-01 16:18:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:30 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: edfd7aae-c4cd-e939-fe4a-45147dc6b8cc
2025-08-01 16:18:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:30 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d654a66e-dada-082d-5517-8e72be395427
2025-08-01 16:18:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:30 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:18:59 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:18:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6a71d954-e870-5993-933d-9e14db01b6a6
2025-08-01 16:18:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:18:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:19:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1a00784b-7e95-d822-1064-a7c15ab4c769
2025-08-01 16:19:06 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:19:06 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:19:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6ef29435-1207-9204-dd87-939f2b2b42e0
2025-08-01 16:19:06 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:19:06 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:19:11 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:11 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 55ae7d74-7826-1000-c3b9-8c0df6eb0993
2025-08-01 16:19:11 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:19:11 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:19:12 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3edc9373-38f8-76c7-8f6a-5ff6305de96a -> completed (进度: 100%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6b4fe026-a213-691b-9f14-bf20e70e233a -> completed (进度: 100%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e28da83c-43bc-7430-b9d3-44773718eaf1 -> processing (进度: 0%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 937df4b5-a3c2-82bf-e832-22b21c4b16b5 -> completed (进度: 100%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: edfd7aae-c4cd-e939-fe4a-45147dc6b8cc -> completed (进度: 100%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d654a66e-dada-082d-5517-8e72be395427 -> completed (进度: 100%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e28da83c-43bc-7430-b9d3-44773718eaf1 -> processing (进度: 0%)
2025-08-01 16:19:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:14 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e28da83c-43bc-7430-b9d3-44773718eaf1 -> completed (进度: 100%)
2025-08-01 16:19:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> processing (进度: 0%)
2025-08-01 16:19:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:38 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 55ae7d74-7826-1000-c3b9-8c0df6eb0993 -> completed (进度: 100%)
2025-08-01 16:19:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> processing (进度: 0%)
2025-08-01 16:19:47 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1a00784b-7e95-d822-1064-a7c15ab4c769 -> completed (进度: 100%)
2025-08-01 16:19:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> processing (进度: 0%)
2025-08-01 16:19:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> processing (进度: 0%)
2025-08-01 16:19:52 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6ef29435-1207-9204-dd87-939f2b2b42e0 -> completed (进度: 100%)
2025-08-01 16:19:52 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:19:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6a71d954-e870-5993-933d-9e14db01b6a6 -> completed (进度: 100%)
2025-08-01 16:20:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ca2797bf-31ad-b4cc-6286-185abfc19597
2025-08-01 16:20:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:20:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:20:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2b195bfb-ecfa-338c-97a3-a9e213e3921f
2025-08-01 16:20:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:20:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:20:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c
2025-08-01 16:20:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:20:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:20:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6205fc94-bb8d-063a-db05-4dad0fd3a143
2025-08-01 16:20:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:20:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:20:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 3bcaf7bc-e017-7e93-756c-523bccef08e2
2025-08-01 16:20:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:20:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:20:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:20:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:20:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:20:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:20:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:20:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> processing (进度: 0%)
2025-08-01 16:21:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:20 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:21:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> processing (进度: 0%)
2025-08-01 16:21:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6205fc94-bb8d-063a-db05-4dad0fd3a143 -> completed (进度: 100%)
2025-08-01 16:21:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:22 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:21:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ca2797bf-31ad-b4cc-6286-185abfc19597 -> completed (进度: 100%)
2025-08-01 16:21:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> processing (进度: 0%)
2025-08-01 16:21:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:25 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:21:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> processing (进度: 0%)
2025-08-01 16:21:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 3bcaf7bc-e017-7e93-756c-523bccef08e2 -> completed (进度: 100%)
2025-08-01 16:21:26 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:21:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2b195bfb-ecfa-338c-97a3-a9e213e3921f -> completed (进度: 100%)
2025-08-01 16:21:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> processing (进度: 0%)
2025-08-01 16:21:30 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:21:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 5bae4edd-efe9-9cfa-ee46-7bc2e172641c -> completed (进度: 100%)
2025-08-01 16:22:18 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7527826a-a014-40dd-77eb-12e6ca76671a
2025-08-01 16:22:18 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:22:18 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:22:18 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ec92cdf8-3552-f5b8-341a-94e3508c08b1
2025-08-01 16:22:18 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:22:18 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:22:19 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 693cbde6-de51-63d6-1497-9118b9e374a2
2025-08-01 16:22:19 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:22:19 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:22:19 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41
2025-08-01 16:22:19 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:22:19 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:22:19 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 60bbf518-c49d-b494-2626-50dafd959048
2025-08-01 16:22:19 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:22:19 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:22:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> processing (进度: 0%)
2025-08-01 16:22:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:57 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7527826a-a014-40dd-77eb-12e6ca76671a -> completed (进度: 100%)
2025-08-01 16:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> processing (进度: 0%)
2025-08-01 16:22:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:22:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:22:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:22:59 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:22:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 8da95d97-5e36-fce7-3cf9-57d5ad0e3e41 -> completed (进度: 100%)
2025-08-01 16:23:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> processing (进度: 0%)
2025-08-01 16:23:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:23:00 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:23:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 693cbde6-de51-63d6-1497-9118b9e374a2 -> completed (进度: 100%)
2025-08-01 16:23:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:23:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> processing (进度: 0%)
2025-08-01 16:23:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:03 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:23:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ec92cdf8-3552-f5b8-341a-94e3508c08b1 -> completed (进度: 100%)
2025-08-01 16:23:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> processing (进度: 0%)
2025-08-01 16:23:09 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:23:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 60bbf518-c49d-b494-2626-50dafd959048 -> completed (进度: 100%)
2025-08-01 16:24:07 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6c36f062-2001-20dc-0b7e-757575256dd8
2025-08-01 16:24:07 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 16:24:07 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 16:24:07 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 16:24:07 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 16:24:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 0%)
2025-08-01 16:24:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:24:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:24:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:24:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:24:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 74%)
2025-08-01 16:25:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 74%)
2025-08-01 16:25:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 80%)
2025-08-01 16:25:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 68%)
2025-08-01 16:25:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 68%)
2025-08-01 16:25:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:25:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:26:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 56%)
2025-08-01 16:26:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> processing (进度: 62%)
2025-08-01 16:26:46 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 16:26:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6c36f062-2001-20dc-0b7e-757575256dd8 -> completed (进度: 100%)
2025-08-01 17:38:24 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：986b8f7d-3edc-de81-5313-d823925f4d66
2025-08-01 17:38:24 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 986b8f7d-3edc-de81-5313-d823925f4d66
2025-08-01 17:38:24 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 17:38:24 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 17:38:25 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': 'dd86faf2c444f82c3661a52df4cd4df5', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '1c7ba176caebcddb0745aea9e1bd5e40', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '7194f8adffcfa1ab9d6919cba7fedef0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 17:38:27 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 17:40:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b70ecdbb-6dc8-216c-c0e8-82f55d21f27b
2025-08-01 17:40:35 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 17:40:35 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b70ecdbb-6dc8-216c-c0e8-82f55d21f27b
2025-08-01 17:40:35 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b70ecdbb-6dc8-216c-c0e8-82f55d21f27b
2025-08-01 17:40:36 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:36 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:36 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:36 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': 'ef357fe47d819dda007e7e341ed8298e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '2a3a6a2c23ad629f53b5031bdbf6accc', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '8cbe065c05397aa6acc48438d35166c5', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 17:40:38 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 17:53:05 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c055cf5c-438b-35f3-121e-13f69afef68e
2025-08-01 17:53:05 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 17:53:05 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-r1
2025-08-01 17:53:05 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"0c095b7b26564a029e4c1a25a7017560","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"0c095b7b26564a029e4c1a25a7017560","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: DeepSeek流式API调用失败: 401 - {"id":"0c095b7b26564a029e4c1a25a7017560","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c055cf5c-438b-35f3-121e-13f69afef68e
2025-08-01 17:53:05 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c055cf5c-438b-35f3-121e-13f69afef68e
2025-08-01 17:53:05 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"300c116ac244d869870e4807e8496264","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"300c116ac244d869870e4807e8496264","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: DeepSeek流式API调用失败: 401 - {"id":"300c116ac244d869870e4807e8496264","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 17:53:06 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:07 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '78e28cc97cddac113484984a93e37953', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试1/3）: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '03b4b9e083151a5c715ad297206a3b78', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试2/3）: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': 'c6aeb1bba202eddb8efa1a6c63ec85ab', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1660 - 试题1生成失败（尝试3/3）: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 17:53:08 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:01:28 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6ca675b5-395a-74a1-6537-6a340bd2f117
2025-08-01 18:01:28 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:01:28 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-chat
2025-08-01 18:01:33 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 6ca675b5-395a-74a1-6537-6a340bd2f117
2025-08-01 18:01:33 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：6ca675b5-395a-74a1-6537-6a340bd2f117
2025-08-01 18:02:02 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 18:02:02 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 18:02:20 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 267e7ca6-6e31-a1a0-bbb2-9751e1bf4836
2025-08-01 18:02:20 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:02:20 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-chat
2025-08-01 18:02:25 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 267e7ca6-6e31-a1a0-bbb2-9751e1bf4836
2025-08-01 18:02:25 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：267e7ca6-6e31-a1a0-bbb2-9751e1bf4836
2025-08-01 18:02:51 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:04:24 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 18:04:24 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 18:06:10 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 682701f2-dd6e-7758-3896-e2980d7b1c61
2025-08-01 18:06:10 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:06:10 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-chat
2025-08-01 18:11:27 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 18:11:27 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 18:11:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9fcbc2e1-bb5c-42ea-3602-5fefaf2b2f06
2025-08-01 18:11:30 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:11:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 18:11:34 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:34 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:34 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:34 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 9fcbc2e1-bb5c-42ea-3602-5fefaf2b2f06
2025-08-01 18:11:34 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：9fcbc2e1-bb5c-42ea-3602-5fefaf2b2f06
2025-08-01 18:11:38 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:38 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:38 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 18:11:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:54 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:13:36 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 18:13:36 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 18:13:45 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 37c83041-a331-7c78-d803-b0994f2bb7a1
2025-08-01 18:13:45 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:13:45 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 18:13:54 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:54 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:54 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:54 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 37c83041-a331-7c78-d803-b0994f2bb7a1
2025-08-01 18:13:54 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：37c83041-a331-7c78-d803-b0994f2bb7a1
2025-08-01 18:13:58 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:58 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:58 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 18:14:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:12 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:14:23 | INFO     | app.core.state_manager:create_task:90 - 创建任务: af421068-ef14-c76e-0252-898b6bf7c5fe
2025-08-01 18:14:23 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:15:47 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:15:47 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:15:47 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:15:47 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: af421068-ef14-c76e-0252-898b6bf7c5fe
2025-08-01 18:15:47 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：af421068-ef14-c76e-0252-898b6bf7c5fe
2025-08-01 18:17:05 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:17:05 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:17:05 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:15 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 18:22:15 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 18:22:19 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f363f2ce-7ee6-3e0c-56fd-bc523e967371
2025-08-01 18:22:19 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:22:19 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 18:22:22 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:22 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:22 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:22 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f363f2ce-7ee6-3e0c-56fd-bc523e967371
2025-08-01 18:22:22 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f363f2ce-7ee6-3e0c-56fd-bc523e967371
2025-08-01 18:22:26 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:26 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:26 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 2 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 3 失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 18:22:30 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:43 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:23:07 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-01 18:23:07 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-01 18:24:18 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 94b32e1c-ca9b-a278-ae71-3f84a033f7f6
2025-08-01 18:24:18 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:24:18 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 18:25:28 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 94b32e1c-ca9b-a278-ae71-3f84a033f7f6
2025-08-01 18:25:28 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：94b32e1c-ca9b-a278-ae71-3f84a033f7f6
2025-08-01 18:25:48 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:26:51 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5abfaa67-1b0c-6b38-c867-9d5d8141723c
2025-08-01 18:26:51 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:28:00 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 5abfaa67-1b0c-6b38-c867-9d5d8141723c
2025-08-01 18:28:00 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：5abfaa67-1b0c-6b38-c867-9d5d8141723c
2025-08-01 18:28:20 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:33:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e01ba0e6-ff2c-5cf8-6ffa-1ef45daacd3f
2025-08-01 18:33:35 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:34:53 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: e01ba0e6-ff2c-5cf8-6ffa-1ef45daacd3f
2025-08-01 18:34:53 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：e01ba0e6-ff2c-5cf8-6ffa-1ef45daacd3f
2025-08-01 18:35:17 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:35:32 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 73d4d92e-bd3c-00c7-6949-eeab9bcd08ee
2025-08-01 18:35:32 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:36:33 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 73d4d92e-bd3c-00c7-6949-eeab9bcd08ee
2025-08-01 18:36:33 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：73d4d92e-bd3c-00c7-6949-eeab9bcd08ee
2025-08-01 18:40:32 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: 
2025-08-01 18:40:32 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: 
2025-08-01 18:40:32 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: 
2025-08-01 18:41:59 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:48:01 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d8655740-ed8d-e250-656b-53363a132a76
2025-08-01 18:48:01 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:49:26 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: d8655740-ed8d-e250-656b-53363a132a76
2025-08-01 18:49:26 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：d8655740-ed8d-e250-656b-53363a132a76
2025-08-01 18:49:52 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:55:51 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1df94161-3908-3229-bedc-2a18de99be84
2025-08-01 18:55:51 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:55:57 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 1df94161-3908-3229-bedc-2a18de99be84
2025-08-01 18:55:57 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：1df94161-3908-3229-bedc-2a18de99be84
2025-08-01 18:57:33 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 18:57:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c2f87a5c-66c3-2863-ead4-5365e97733b9
2025-08-01 18:57:50 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 18:58:53 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: c2f87a5c-66c3-2863-ead4-5365e97733b9
2025-08-01 18:58:53 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c2f87a5c-66c3-2863-ead4-5365e97733b9
2025-08-01 18:59:24 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 19:02:09 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4cd5afef-36e4-0661-1aff-76d343511dc9
2025-08-01 19:02:09 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 19:03:20 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 4cd5afef-36e4-0661-1aff-76d343511dc9
2025-08-01 19:03:20 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：4cd5afef-36e4-0661-1aff-76d343511dc9
2025-08-01 19:03:55 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 19:04:27 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7226a01a-b57d-8e3f-35be-b47c6fc74d3c
2025-08-01 19:04:27 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 19:05:49 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 7226a01a-b57d-8e3f-35be-b47c6fc74d3c
2025-08-01 19:05:49 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：7226a01a-b57d-8e3f-35be-b47c6fc74d3c
2025-08-01 19:06:28 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 19:07:28 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 44448915-69d4-11a3-b1f8-4066bad07d55
2025-08-01 19:07:28 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-01 19:08:06 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 44448915-69d4-11a3-b1f8-4066bad07d55
2025-08-01 19:08:06 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：44448915-69d4-11a3-b1f8-4066bad07d55
2025-08-01 19:08:50 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1264 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-01 19:11:38 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-01 19:13:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7d7ed0c9-d6e4-8995-c572-414b17345642
2025-08-01 19:13:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 19:13:43 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:13:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d5d2b4e8-6b45-a7d9-6554-6086611927dd
2025-08-01 19:13:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 19:13:43 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:13:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: fadafcd9-d065-7ffa-6319-19596748ca51
2025-08-01 19:13:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-01 19:13:43 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:13:43 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': 'a3f327b7bc9d48fc059326263b2ead7c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.question_cloner:_generate_cloned_content:816 - LLM生成内容失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:13:43 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': 'f66b497ae572a09b9f6544c4db0a755c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 401 - {'id': '7d493412b0d8a0ccf175f9768079cf2f', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.question_cloner:_generate_cloned_content:816 - LLM生成内容失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.question_cloner:_generate_cloned_content:816 - LLM生成内容失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:13:43 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:13:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: d5d2b4e8-6b45-a7d9-6554-6086611927dd -> completed (进度: 100%)
2025-08-01 19:13:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7d7ed0c9-d6e4-8995-c572-414b17345642 -> completed (进度: 100%)
2025-08-01 19:13:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: fadafcd9-d065-7ffa-6319-19596748ca51 -> completed (进度: 100%)
2025-08-01 19:22:28 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0
2025-08-01 19:22:28 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:22:28 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:22:28 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1b31f4d9-fcf7-8b64-829a-379b148a8b23
2025-08-01 19:22:28 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:22:28 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:22:28 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e59d9221-11ec-ea98-32b0-1c2b56e3f568
2025-08-01 19:22:28 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:22:28 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> processing (进度: 0%)
2025-08-01 19:22:35 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> processing (进度: 0%)
2025-08-01 19:22:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 23ff424e-dbf8-2f78-1459-fdc1b6d5acd0 -> completed (进度: 100%)
2025-08-01 19:22:37 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:22:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1b31f4d9-fcf7-8b64-829a-379b148a8b23 -> completed (进度: 100%)
2025-08-01 19:22:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> processing (进度: 0%)
2025-08-01 19:22:38 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:22:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e59d9221-11ec-ea98-32b0-1c2b56e3f568 -> completed (进度: 100%)
2025-08-01 19:23:41 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 20fe016c-412a-6ca0-a2b1-c56494705938
2025-08-01 19:23:41 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:23:41 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:23:41 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bffb6561-d312-febb-384d-0f77d0cd1d3d
2025-08-01 19:23:41 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:23:41 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:23:41 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f63c03d9-44d8-fc23-cbc1-b3b692764401
2025-08-01 19:23:41 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:23:41 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:23:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> processing (进度: 0%)
2025-08-01 19:23:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:57 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:23:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:23:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f63c03d9-44d8-fc23-cbc1-b3b692764401 -> completed (进度: 100%)
2025-08-01 19:23:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> processing (进度: 0%)
2025-08-01 19:23:58 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:23:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 20fe016c-412a-6ca0-a2b1-c56494705938 -> completed (进度: 100%)
2025-08-01 19:23:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:24:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:24:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> processing (进度: 0%)
2025-08-01 19:24:02 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:24:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bffb6561-d312-febb-384d-0f77d0cd1d3d -> completed (进度: 100%)
2025-08-01 19:25:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e8e1ade0-e430-60bd-b976-49efde66a7ce
2025-08-01 19:25:49 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:25:49 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:25:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 351a36ed-e976-d999-85cd-94dfc4cd9c79
2025-08-01 19:25:49 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:25:49 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:25:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 598e9a68-e2ed-2019-052a-70bc5876c282
2025-08-01 19:25:49 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:25:49 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:25:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> processing (进度: 0%)
2025-08-01 19:25:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> processing (进度: 0%)
2025-08-01 19:25:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> processing (进度: 0%)
2025-08-01 19:25:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> processing (进度: 0%)
2025-08-01 19:25:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> processing (进度: 0%)
2025-08-01 19:25:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> processing (进度: 0%)
2025-08-01 19:25:56 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:25:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> processing (进度: 0%)
2025-08-01 19:25:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: e8e1ade0-e430-60bd-b976-49efde66a7ce -> completed (进度: 100%)
2025-08-01 19:25:57 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:25:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:25:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 598e9a68-e2ed-2019-052a-70bc5876c282 -> completed (进度: 100%)
2025-08-01 19:25:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> processing (进度: 0%)
2025-08-01 19:26:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:26:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 351a36ed-e976-d999-85cd-94dfc4cd9c79 -> completed (进度: 100%)
2025-08-01 19:26:37 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9b06fe51-a1e2-17bd-815c-b058bec524ba
2025-08-01 19:26:37 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:26:37 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:26:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> processing (进度: 0%)
2025-08-01 19:26:51 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:26:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9b06fe51-a1e2-17bd-815c-b058bec524ba -> completed (进度: 100%)
2025-08-01 19:27:08 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689
2025-08-01 19:27:08 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:27:08 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:27:08 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b91d17bf-5bb5-7c39-a715-6a6c17c1905a
2025-08-01 19:27:08 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:27:08 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:27:08 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6dc2acaf-6632-173c-49c5-46c6151a3f46
2025-08-01 19:27:08 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:27:08 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:27:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> processing (进度: 0%)
2025-08-01 19:27:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:24 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:27:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> processing (进度: 0%)
2025-08-01 19:27:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 7f3a2c0b-d5a9-a18b-db85-d59c15b69689 -> completed (进度: 100%)
2025-08-01 19:27:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:25 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:27:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 6dc2acaf-6632-173c-49c5-46c6151a3f46 -> completed (进度: 100%)
2025-08-01 19:27:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> processing (进度: 0%)
2025-08-01 19:27:46 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:27:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: b91d17bf-5bb5-7c39-a715-6a6c17c1905a -> completed (进度: 100%)
2025-08-01 19:28:27 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ea9951a7-aea2-d10c-f2c7-4130b9b0b006
2025-08-01 19:28:27 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:28:27 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:28:27 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 19:28:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 0%)
2025-08-01 19:28:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 80%)
2025-08-01 19:28:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 80%)
2025-08-01 19:28:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 80%)
2025-08-01 19:28:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 80%)
2025-08-01 19:28:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 80%)
2025-08-01 19:28:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 74%)
2025-08-01 19:28:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 68%)
2025-08-01 19:28:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 56%)
2025-08-01 19:28:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 56%)
2025-08-01 19:28:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> processing (进度: 56%)
2025-08-01 19:28:54 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:28:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ea9951a7-aea2-d10c-f2c7-4130b9b0b006 -> completed (进度: 100%)
2025-08-01 19:29:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: bf7711e5-47dd-d61f-580a-44063c5856f9
2025-08-01 19:29:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-01 19:29:43 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-01 19:29:43 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 19:29:43 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-01 19:29:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:29:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:30:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 0%)
2025-08-01 19:30:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 80%)
2025-08-01 19:30:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 80%)
2025-08-01 19:30:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 80%)
2025-08-01 19:30:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 80%)
2025-08-01 19:30:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 80%)
2025-08-01 19:30:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 80%)
2025-08-01 19:30:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 62%)
2025-08-01 19:30:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 74%)
2025-08-01 19:30:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 74%)
2025-08-01 19:30:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 74%)
2025-08-01 19:30:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 90%)
2025-08-01 19:30:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> processing (进度: 90%)
2025-08-01 19:30:13 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-01 19:30:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: bf7711e5-47dd-d61f-580a-44063c5856f9 -> completed (进度: 100%)
