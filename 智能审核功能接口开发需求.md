### 接口定义
审题服务接口名称：post  /api/verifyquestion
审题服务轮询查询接口名称： post /api/getverifystate

### 参数定义

#### 审题服务接口
入参：
与/AgentGen接口入参一致
```json
{
    "QuesPost": { // 试题业务信息   智能命题\试题克隆 使用
        "ExamProjectName": "湖北省学考合格考命审题与题库管理系统",
        "ExamSubjectName": "语文",
        "AiAssignSupplement": "{\"命题知识点\":\"项脊轩志\"}",
        "AiQuesTypePost": {
            "QuesPrompt": {},
            "Elements": [
                {
                    "ElementType": "langMaterial",
                    "ElementText": []
                }
            ],
            "BaseName": "组合题",
            "QuesTypeId": "992504162017020080002443632807",
            "QuesTypeName": "组合题",
            "ChoiceCount": 0,
            "QuesCount": 1,
            "QuesStr": null,
            "QuesProps": [
                {
                    "QuesPropName": "必备知识",
                    "QuesPropId": "992502191515283100003958726989",
                    "QuesPropSource": [
                        {
                            "Id": "992503071613473570010247655701",
                            "text": "必修/中国特色社会主义",
                            "remark": ""
                        },
                        {
                            "Id": "992503071614036650010287333506",
                            "text": "必修/经济与社会",
                            "remark": ""
                        },
                        {
                            "Id": "992503071614102250010310231558",
                            "text": "必修/政治与法治",
                            "remark": ""
                        },
                        {
                            "Id": "992503071614581480010373853188",
                            "text": "必修/哲学与文化",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615183500010410006045",
                            "text": "选择性必修/当代国际政治与经济",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615239060010445616923",
                            "text": "选择性必修/法律与生活",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615287830010462568267",
                            "text": "选择性必修/逻辑与思维",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615487860010506538985",
                            "text": "选修课/财经与生活",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615543390010536263460",
                            "text": "选修课/法官与律师",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615595400010551680775",
                            "text": "选修课/历史上的哲学家",
                            "remark": ""
                        }
                    ],
                    "QuesPropArr": [
                        "必修/中国特色社会主义",
                        "必修/经济与社会",
                        "必修/政治与法治",
                        "必修/哲学与文化",
                        "选择性必修/当代国际政治与经济",
                        "选择性必修/法律与生活",
                        "选择性必修/逻辑与思维",
                        "选修课/财经与生活",
                        "选修课/法官与律师",
                        "选修课/历史上的哲学家"
                    ],
                    "SelectQuesPropText": "",
                    "NotifyType": "5",
                    "ValueObj": null,
                    "SelectQuesPropRemark": ""
                },
                {
                    "QuesPropName": "难度等级",
                    "QuesPropId": "992502191537002900008718163513",
                    "QuesPropSource": [
                        {
                            "Id": "992502191626430980014638761665",
                            "text": "难",
                            "remark": "试题内容复杂、考查要求高，主要考查考生的创新思维和解决复杂问题的能力。"
                        },
                        {
                            "Id": "992502191626477010014682660805",
                            "text": "中",
                            "remark": "试题内容较为复杂、考查要求较高，主要考查考生的综合思维能力和实践能力。"
                        },
                        {
                            "Id": "992502191626519030014732126300",
                            "text": "易",
                            "remark": "试题内容简单、考查要求较低，主要考查考生的基础知识和基本技能"
                        }
                    ],
                    "QuesPropArr": [
                        "难",
                        "中",
                        "易"
                    ],
                    "SelectQuesPropText": "",
                    "NotifyType": "2",
                    "ValueObj": null,
                    "SelectQuesPropRemark": ""
                },
                {
                    "QuesPropName": "考查要求",
                    "QuesPropId": "992502191537523980008886556169",
                    "QuesPropSource": [
                        {
                            "Id": "992502191709406670017727564216",
                            "text": "基础性",
                            "remark": "考查学生对主干知识和基本理论的掌握程度，关注今后生活、学习和工作所必须具备、不可或缺的知识、能力和素养。"
                        },
                        {
                            "Id": "992502191709463640017770356935",
                            "text": "综合性",
                            "remark": "体现在学科价值观与社会主义核心价值观的有机结合，考查学生对知识体系的系统性和整体性把握能力"
                        },
                        {
                            "Id": "992502191709507080017824636662",
                            "text": "应用性",
                            "remark": "体现在运用学科的知识和能力发现问题、分析问题、解决问题，运用正确的价值观和方法论，总结历史经验教训，为现实提供有意义、有价值的借鉴"
                        },
                        {
                            "Id": "992502191709551670017874982547",
                            "text": "创新性",
                            "remark": "体现在对科目资料进行新的解释和新的运用，对事物之间的联系进行新的发掘；对已有的观点、方法与结论进行批判性思考，得出新结论。"
                        }
                    ],
                    "QuesPropArr": [
                        "基础性",
                        "综合性",
                        "应用性",
                        "创新性"
                    ],
                    "SelectQuesPropText": "",
                    "NotifyType": "5",
                    "ValueObj": null,
                    "SelectQuesPropRemark": ""
                }
            ],
            "Childs": [
            ]
        }
    },
    "WorkflowPost": { // 模型信息   智能命题\试题克隆\智能生成 使用
        "NodeList": [
            {
                "NodeName": "试题命制",
                "NodeType": "AiSinglePrompt",
                "NodeContent": [
                    {
                        "NodePrompt": {
                            "功能": "按照给出的试题样例、试题类型、试题属性等信息及要求对试题进行命制",
                            "工作流": "1. 严格按照给出的试题结构等试题信息生成试题"
                        },
                        "NodeModel": {
                            "ModelName": "deepseek-r1",
                            "ModelArgs": {
                                "TopP": "0.5",
                                "TopK": "5",
                                "Temperature": "0.8",
                                "MaxToken": "2048",
                                "ApiKey": "sk-4z16Z80psmCcTTbPr7oxIXCTSZUAaoWwWtswRPifj1TXuVLA",
                                "ChatUrl": "https://api.lkeap.cloud.tencent.com/v1"
                            }
                        }
                    }
                ]
            }
        ]
    },
    "AgentPost": { // 任务角色信息   智能命题\试题克隆\智能生成 部分使用 ChatContent信息不是用，仅在智能生成使用
        "NodeContent": [
            {
                "NodePrompt": {
                    "总体任务": "根据给出的试题相关信息，如知识点、试题命制要求、试题结构、试题样例等信息对试题进行命制。",
                    "总体要求": "命制的试题质量要高，要素齐全，满足题型要求"
                },
                "NodeModel": null
            }
        ],
        "ChatContent": ""
    },
    "TaskName": "智能命题",
    "TaskId": "05f36ef4-c166-4311-b27d-68e814204908"
}```
响应：

#### 审题服务轮询查询接口

入参：
```json
{
    "taskId":str // 任务ID
}
```

响应：
```json
{
    "code": 0, // 状态码
    "msg": "string",  // 状态消息
    "data": {
        "taskId": "string", // 任务ID
        "stepList": [   // 步骤列表
            {
                "stepName": "string", // 当前审核任务步骤名称
                "stepStatus": 0, // 审核通过状态
                "failedItems": [   // 审核项结果
                    {
                        "itemName": "string",  // 审核项名称 
                        "suggestionItems": [  // 审核建议项列表 按照试题元素
                            {
                                "reason": "string",  // 详细原因
                                "suggestion": "string", // 修改建议
                                "inCorrect": "string", // 待矫正内容
                                "correct": "string", // 矫正内容
                                "element": "string" // 元素名称 试题元素
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

### 业务需求

#### 业务特点

1. 对于试题进行多项审核，包括试题内容检测、表达规范检测、试题属性检测、合规性检测

#### 业务功能实现逻辑

1. 试题内容审核包括：试题内容完整性检测（内容是否完整，是否缺失，是否有信息的缺失或者标点符号的缺失），参考答案、解析、评分标准（如有）是否完整准
2. 表达规范检测：试题内容是否存在错别字，拼写错误，标点符号使用错误
3. 属性检测：试题知识点是否标注有误，难度层次是否标注有误
4. 合规性检测：敏感词检测、教育公平性检测（地域歧视、文化歧视、价值观歧视等）、命题题型规范检测（命制题型是否符合试题的要求，如选择题只有一个答案，选项内部不应有逻辑漏洞，正确选项与错误选项之间有着明显的区分度等）

### 开发要求

1. 试题内容审核：通过与大语言模型的交互，完成审核。
2. 表达规范检测：通过与大语言模型的交互，完成审核。
3. 属性检测：通过与大语言模型的交互，完成审核。
4. 合规性检测：通过传统的敏感词库结合大模型交互完成审核。

#### 技术设计

1. 使用ReAct—Agent技术，并构建多个审查任务Agent，各司其职，完成审查工作，要求每一个Agent类具有可拓展性，信息可补充性（通过comfig.yaml配置文件更新），推荐使用langgraph库。
2. 按照服务接口的设计，保证多个审核服务并发执行，程序中及时更新状态，保证轮询接口能够快速准确的获取审核服务进度。