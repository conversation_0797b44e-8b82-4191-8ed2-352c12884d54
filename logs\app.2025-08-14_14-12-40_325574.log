2025-08-14 14:12:40 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:12:40 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:13:15 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ca0ed131-f2f2-8a86-9b5f-680b36fbb25c
2025-08-14 14:13:15 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:13:15 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:13:47 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4b69d4cb-8106-b709-2bae-1fe6ae2e923e
2025-08-14 14:13:47 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:13:47 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 14:24:03 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:24:03 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:24:15 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f1fd682a-f5b9-6231-f21f-f7bdb27da3e0
2025-08-14 14:24:15 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:24:15 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:26:01 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f1fd682a-f5b9-6231-f21f-f7bdb27da3e0
2025-08-14 14:28:07 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f5f770ab-ec3d-974f-b05d-a6949ba1b696', 'request_id': 'f5f770ab-ec3d-974f-b05d-a6949ba1b696'}
2025-08-14 14:28:07 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:742 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1600 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:08 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7006e0f4-d139-95f4-9fba-643e51157f53', 'request_id': '7006e0f4-d139-95f4-9fba-643e51157f53'}
2025-08-14 14:28:08 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:742 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1600 - 试题1生成失败（尝试2/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:09 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-8e0d98c2-08aa-9f1e-8df3-cf144cae39aa', 'request_id': '8e0d98c2-08aa-9f1e-8df3-cf144cae39aa'}
2025-08-14 14:28:09 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:742 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1600 - 试题1生成失败（尝试3/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1609 - 试题1生成最终失败，已重试3次
2025-08-14 14:28:09 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1564 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:28:47 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:28:47 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:29:05 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1968dc59-61fd-7314-a363-99516b52774b
2025-08-14 14:29:05 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:29:05 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:30:13 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 1968dc59-61fd-7314-a363-99516b52774b
2025-08-14 14:30:13 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：1968dc59-61fd-7314-a363-99516b52774b
2025-08-14 14:30:26 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:31:14 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:31:14 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:31:23 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 228f225f-8072-d32f-ba87-a321cd3913c7
2025-08-14 14:31:23 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:31:23 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:31:39 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 228f225f-8072-d32f-ba87-a321cd3913c7
2025-08-14 14:31:39 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：228f225f-8072-d32f-ba87-a321cd3913c7
2025-08-14 14:31:54 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:33:39 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:33:39 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:33:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6997f827-9459-29b7-11df-ee34161d86ca
2025-08-14 14:33:49 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:33:49 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:34:34 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 6997f827-9459-29b7-11df-ee34161d86ca
2025-08-14 14:34:34 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：6997f827-9459-29b7-11df-ee34161d86ca
2025-08-14 14:34:47 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:35:28 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:35:28 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:35:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d8a14fac-185c-c7e5-0634-50d06dcb816b
2025-08-14 14:35:35 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:35:35 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:36:00 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: d8a14fac-185c-c7e5-0634-50d06dcb816b
2025-08-14 14:36:00 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：d8a14fac-185c-c7e5-0634-50d06dcb816b
2025-08-14 14:36:17 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:37:48 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4d5ad1b0-6373-ea78-59ff-bd9ed0f89ac6
2025-08-14 14:37:48 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:38:08 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 4d5ad1b0-6373-ea78-59ff-bd9ed0f89ac6
2025-08-14 14:38:08 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：4d5ad1b0-6373-ea78-59ff-bd9ed0f89ac6
2025-08-14 14:39:05 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:41:58 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:41:58 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:42:08 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 603c51b6-3bb9-03a7-7fc3-8686bdf806fe
2025-08-14 14:42:08 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:42:08 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:42:27 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 603c51b6-3bb9-03a7-7fc3-8686bdf806fe
2025-08-14 14:42:27 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：603c51b6-3bb9-03a7-7fc3-8686bdf806fe
2025-08-14 14:42:41 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:42:48 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a4b5dab6-bcda-c82b-139d-7c17412a85d4
2025-08-14 14:42:48 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:43:10 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a4b5dab6-bcda-c82b-139d-7c17412a85d4
2025-08-14 14:43:10 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a4b5dab6-bcda-c82b-139d-7c17412a85d4
2025-08-14 14:43:21 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:45:39 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4032227f-5bac-acc3-f9e7-ffb7aaa33a4b
2025-08-14 14:45:39 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:45:39 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 14:47:10 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 14:47:10 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 14:47:21 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 15c7cc92-36f3-7a96-ab27-7b23e4ea41e2
2025-08-14 14:47:21 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:47:21 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-14 14:47:47 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 15c7cc92-36f3-7a96-ab27-7b23e4ea41e2
2025-08-14 14:47:47 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：15c7cc92-36f3-7a96-ab27-7b23e4ea41e2
2025-08-14 14:48:02 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:50:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cee74d81-f0c7-29c8-c30a-9e8f3f704907
2025-08-14 14:50:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:50:31 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 14:50:38 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: cee74d81-f0c7-29c8-c30a-9e8f3f704907
2025-08-14 14:50:38 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：cee74d81-f0c7-29c8-c30a-9e8f3f704907
2025-08-14 14:50:59 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:55:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a7f454d4-4171-6ae3-cd61-58e27ca91ca2
2025-08-14 14:55:53 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 14:56:05 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a7f454d4-4171-6ae3-cd61-58e27ca91ca2
2025-08-14 14:56:05 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a7f454d4-4171-6ae3-cd61-58e27ca91ca2
2025-08-14 14:57:06 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 14:59:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a0b85889-768a-a38d-3135-21cd35b1a59a
2025-08-14 14:59:59 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:00:12 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a0b85889-768a-a38d-3135-21cd35b1a59a
2025-08-14 15:00:12 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a0b85889-768a-a38d-3135-21cd35b1a59a
2025-08-14 15:00:26 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:04:10 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 98e8e482-2ba4-9695-c2f2-576590c538db
2025-08-14 15:04:10 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:04:26 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 98e8e482-2ba4-9695-c2f2-576590c538db
2025-08-14 15:04:26 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：98e8e482-2ba4-9695-c2f2-576590c538db
2025-08-14 15:04:41 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:05:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 4510848d-42ee-0f3e-f6d5-daad23b6f643
2025-08-14 15:05:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:05:44 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 4510848d-42ee-0f3e-f6d5-daad23b6f643
2025-08-14 15:05:44 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：4510848d-42ee-0f3e-f6d5-daad23b6f643
2025-08-14 15:06:04 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:38:16 | INFO     | app.core.state_manager:create_task:90 - 创建任务: af7d34d0-fcb2-30f4-0336-6d38cb4d03ce
2025-08-14 15:38:16 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:38:36 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: af7d34d0-fcb2-30f4-0336-6d38cb4d03ce
2025-08-14 15:38:36 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：af7d34d0-fcb2-30f4-0336-6d38cb4d03ce
2025-08-14 15:39:01 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题1生成失败（尝试1/3）: 'count'
2025-08-14 15:39:01 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题2生成失败（尝试1/3）: 'count'
2025-08-14 15:39:01 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题3生成失败（尝试1/3）: 'count'
2025-08-14 15:39:01 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题1生成失败（尝试2/3）: 'count'
2025-08-14 15:39:01 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题2生成失败（尝试2/3）: 'count'
2025-08-14 15:39:01 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题3生成失败（尝试2/3）: 'count'
2025-08-14 15:39:02 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题1生成失败（尝试3/3）: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题1生成最终失败，已重试3次
2025-08-14 15:39:02 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题2生成失败（尝试3/3）: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题2生成最终失败，已重试3次
2025-08-14 15:39:02 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题3生成失败（尝试3/3）: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题3生成最终失败，已重试3次
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题1生成最终失败: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题2生成最终失败: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题3生成最终失败: 'count'
2025-08-14 15:39:02 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:41:47 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8cb009bb-72ee-779f-4971-54efce8b2de1
2025-08-14 15:41:47 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:42:04 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8cb009bb-72ee-779f-4971-54efce8b2de1
2025-08-14 15:42:04 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8cb009bb-72ee-779f-4971-54efce8b2de1
2025-08-14 15:42:39 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:43:33 | INFO     | app.core.state_manager:create_task:90 - 创建任务: dae3d822-327b-1f87-19b1-caadc9bca1f7
2025-08-14 15:43:33 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:43:50 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: dae3d822-327b-1f87-19b1-caadc9bca1f7
2025-08-14 15:43:50 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：dae3d822-327b-1f87-19b1-caadc9bca1f7
2025-08-14 15:44:16 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题1生成失败（尝试1/3）: 'count'
2025-08-14 15:44:16 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题2生成失败（尝试1/3）: 'count'
2025-08-14 15:44:16 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题3生成失败（尝试1/3）: 'count'
2025-08-14 15:44:16 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题1生成失败（尝试2/3）: 'count'
2025-08-14 15:44:16 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题2生成失败（尝试2/3）: 'count'
2025-08-14 15:44:16 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题3生成失败（尝试2/3）: 'count'
2025-08-14 15:44:17 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题1生成失败（尝试3/3）: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题1生成最终失败，已重试3次
2025-08-14 15:44:17 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题2生成失败（尝试3/3）: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题2生成最终失败，已重试3次
2025-08-14 15:44:17 | WARNING  | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:881 - 子题3生成失败（尝试3/3）: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题3生成最终失败，已重试3次
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题1生成最终失败: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题2生成最终失败: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题3生成最终失败: 'count'
2025-08-14 15:44:17 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:50:30 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-14 15:50:30 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-14 15:50:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6b6425e1-763b-8ecf-873e-88ee4c572f2f
2025-08-14 15:50:43 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:50:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 15:51:05 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 6b6425e1-763b-8ecf-873e-88ee4c572f2f
2025-08-14 15:51:05 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：6b6425e1-763b-8ecf-873e-88ee4c572f2f
2025-08-14 15:51:54 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:52:47 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 667475e8-716b-0688-7583-5cf9a7eb0101
2025-08-14 15:52:47 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:53:08 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 667475e8-716b-0688-7583-5cf9a7eb0101
2025-08-14 15:53:08 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：667475e8-716b-0688-7583-5cf9a7eb0101
2025-08-14 15:53:08 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-14 15:54:54 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:56:21 | INFO     | app.core.state_manager:create_task:90 - 创建任务: dff19783-ac15-e18d-d168-c0a6abec1da9
2025-08-14 15:56:21 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:56:46 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: dff19783-ac15-e18d-d168-c0a6abec1da9
2025-08-14 15:56:46 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：dff19783-ac15-e18d-d168-c0a6abec1da9
2025-08-14 15:57:38 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:58:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1fda3161-6a27-a290-7c65-133238f071de
2025-08-14 15:58:06 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 15:58:29 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 1fda3161-6a27-a290-7c65-133238f071de
2025-08-14 15:58:29 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：1fda3161-6a27-a290-7c65-133238f071de
2025-08-14 15:59:28 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 15:59:48 | INFO     | app.core.state_manager:create_task:90 - 创建任务: b6f4507d-7bbb-9224-023c-23d955dd78cd
2025-08-14 15:59:48 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:00:01 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: b6f4507d-7bbb-9224-023c-23d955dd78cd
2025-08-14 16:00:01 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：b6f4507d-7bbb-9224-023c-23d955dd78cd
2025-08-14 16:00:24 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:03:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a6620a0b-852b-7bd0-dd3d-bcb41ae9e233
2025-08-14 16:03:04 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:03:25 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: a6620a0b-852b-7bd0-dd3d-bcb41ae9e233
2025-08-14 16:03:25 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：a6620a0b-852b-7bd0-dd3d-bcb41ae9e233
2025-08-14 16:03:47 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:06:02 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e1842f88-dfe0-1bf2-7421-e02ec996a31f
2025-08-14 16:06:02 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:06:15 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: e1842f88-dfe0-1bf2-7421-e02ec996a31f
2025-08-14 16:06:15 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：e1842f88-dfe0-1bf2-7421-e02ec996a31f
2025-08-14 16:06:35 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:08:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 825f307d-aa09-d2d4-82a6-79af87da1118
2025-08-14 16:08:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:08:47 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 825f307d-aa09-d2d4-82a6-79af87da1118
2025-08-14 16:08:47 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：825f307d-aa09-d2d4-82a6-79af87da1118
2025-08-14 16:09:13 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:16:00 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 405766ce-5915-1304-49d3-a8962cf0f84d
2025-08-14 16:16:00 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:16:17 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 405766ce-5915-1304-49d3-a8962cf0f84d
2025-08-14 16:16:17 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：405766ce-5915-1304-49d3-a8962cf0f84d
2025-08-14 16:16:35 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:18:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d8adbee3-34cc-78ea-0753-419a00fb5252
2025-08-14 16:18:30 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:18:45 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: d8adbee3-34cc-78ea-0753-419a00fb5252
2025-08-14 16:18:45 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：d8adbee3-34cc-78ea-0753-419a00fb5252
2025-08-14 16:19:05 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:19:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c7c3083e-a011-572b-6ce4-27acaa59a15d
2025-08-14 16:19:49 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:20:23 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c7c3083e-a011-572b-6ce4-27acaa59a15d
2025-08-14 16:20:23 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c7c3083e-a011-572b-6ce4-27acaa59a15d
2025-08-14 16:20:48 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:21:49 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7bd2c4b8-2884-7cc0-cde5-6716c906e1bd
2025-08-14 16:21:49 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:22:10 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 7bd2c4b8-2884-7cc0-cde5-6716c906e1bd
2025-08-14 16:22:10 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：7bd2c4b8-2884-7cc0-cde5-6716c906e1bd
2025-08-14 16:22:48 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:29:44 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 14114e99-46a8-aac1-fe35-7aae4ca212bc
2025-08-14 16:29:44 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 16:29:44 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-14 16:29:44 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 989329f1-1054-727a-8df6-9fcdba7ff440
2025-08-14 16:29:44 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 16:29:44 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-14 16:29:44 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1696838e-bf7b-cec9-574e-ed761cbbcb96
2025-08-14 16:29:44 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 16:29:44 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-14 16:29:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:29:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:29:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:29:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:30:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:30:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:30:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:30:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:30:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> processing (进度: 0%)
2025-08-14 16:30:05 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-14 16:30:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 14114e99-46a8-aac1-fe35-7aae4ca212bc -> completed (进度: 100%)
2025-08-14 16:30:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> processing (进度: 0%)
2025-08-14 16:30:09 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-14 16:30:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 1696838e-bf7b-cec9-574e-ed761cbbcb96 -> completed (进度: 100%)
2025-08-14 16:30:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> processing (进度: 0%)
2025-08-14 16:30:12 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-14 16:30:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 989329f1-1054-727a-8df6-9fcdba7ff440 -> completed (进度: 100%)
2025-08-14 16:31:01 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2d5d358f-87ad-8032-edcc-626ddab5d208
2025-08-14 16:31:01 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 16:31:01 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-14 16:31:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> processing (进度: 0%)
2025-08-14 16:31:12 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-14 16:31:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 2d5d358f-87ad-8032-edcc-626ddab5d208 -> completed (进度: 100%)
2025-08-14 16:31:55 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1
2025-08-14 16:31:55 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 16:31:55 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-14 16:31:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:31:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:31:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:31:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> processing (进度: 0%)
2025-08-14 16:32:22 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-14 16:32:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 9e0ce1f5-2dc2-88dc-82d5-af8cb1509be1 -> completed (进度: 100%)
2025-08-14 16:33:01 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 163e28bb-0f16-069b-a6d6-a86a1aa6c628
2025-08-14 16:33:01 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-14 16:33:01 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-14 16:33:01 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-08-14 16:33:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 0%)
2025-08-14 16:33:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 80%)
2025-08-14 16:33:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 74%)
2025-08-14 16:33:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 74%)
2025-08-14 16:33:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 74%)
2025-08-14 16:33:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 74%)
2025-08-14 16:33:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 74%)
2025-08-14 16:33:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 74%)
2025-08-14 16:33:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 56%)
2025-08-14 16:33:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 56%)
2025-08-14 16:33:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 56%)
2025-08-14 16:33:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:40 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:44 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:45 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:46 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:47 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:48 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:49 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:50 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:51 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:52 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:53 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:54 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:55 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:56 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:33:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 62%)
2025-08-14 16:34:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:10 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:16 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:17 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:18 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:19 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:20 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:21 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:22 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:23 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:24 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:25 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:26 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:27 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:28 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:29 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:30 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:31 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:32 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:33 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:34 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:35 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:36 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:37 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:38 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:39 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:41 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:42 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> processing (进度: 68%)
2025-08-14 16:34:42 | WARNING  | app.services.question_cloner:clone_single_child:406 - 子题目5LLM生成失败，保持原内容
2025-08-14 16:34:42 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-14 16:34:43 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 163e28bb-0f16-069b-a6d6-a86a1aa6c628 -> completed (进度: 100%)
2025-08-14 16:35:40 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ef088f48-f552-75ab-3e0e-589956507a55
2025-08-14 16:35:40 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:36:06 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: ef088f48-f552-75ab-3e0e-589956507a55
2025-08-14 16:36:06 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：ef088f48-f552-75ab-3e0e-589956507a55
2025-08-14 16:37:02 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-14 16:37:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 57ba4080-b619-70a9-1c77-e0e0fc22de2c
2025-08-14 16:37:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-14 16:37:53 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 57ba4080-b619-70a9-1c77-e0e0fc22de2c
2025-08-14 16:37:53 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：57ba4080-b619-70a9-1c77-e0e0fc22de2c
2025-08-14 16:38:51 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-15 08:33:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 50cb657e-a87c-6fd7-1755-d6b9178d502a
2025-08-15 08:33:04 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 08:33:04 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 08:33:05 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f9e02032-ea04-161d-7fd1-66a9d477cc97
2025-08-15 08:33:05 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 08:33:05 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 08:33:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 50cb657e-a87c-6fd7-1755-d6b9178d502a -> processing (进度: 0%)
2025-08-15 08:33:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9e02032-ea04-161d-7fd1-66a9d477cc97 -> processing (进度: 0%)
2025-08-15 08:33:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 50cb657e-a87c-6fd7-1755-d6b9178d502a -> processing (进度: 0%)
2025-08-15 08:33:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9e02032-ea04-161d-7fd1-66a9d477cc97 -> processing (进度: 0%)
2025-08-15 08:33:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9e02032-ea04-161d-7fd1-66a9d477cc97 -> processing (进度: 0%)
2025-08-15 08:33:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 50cb657e-a87c-6fd7-1755-d6b9178d502a -> processing (进度: 0%)
2025-08-15 08:33:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 50cb657e-a87c-6fd7-1755-d6b9178d502a -> processing (进度: 0%)
2025-08-15 08:33:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9e02032-ea04-161d-7fd1-66a9d477cc97 -> processing (进度: 0%)
2025-08-15 08:33:09 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 08:33:09 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 08:33:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: f9e02032-ea04-161d-7fd1-66a9d477cc97 -> completed (进度: 100%)
2025-08-15 08:33:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 50cb657e-a87c-6fd7-1755-d6b9178d502a -> completed (进度: 100%)
2025-08-15 08:33:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: fecfa33c-7e6e-5a6e-e949-7d3796235d9b
2025-08-15 08:33:53 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-15 08:33:53 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 08:34:14 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: fecfa33c-7e6e-5a6e-e949-7d3796235d9b
2025-08-15 08:34:14 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：fecfa33c-7e6e-5a6e-e949-7d3796235d9b
2025-08-15 08:34:24 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-15 09:07:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a
2025-08-15 09:07:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 09:07:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 09:07:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4
2025-08-15 09:07:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 09:07:59 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 09:08:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> processing (进度: 0%)
2025-08-15 09:08:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> processing (进度: 0%)
2025-08-15 09:08:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> processing (进度: 0%)
2025-08-15 09:08:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> processing (进度: 0%)
2025-08-15 09:08:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> processing (进度: 0%)
2025-08-15 09:08:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> processing (进度: 0%)
2025-08-15 09:08:06 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 09:08:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 054057b8-c2dc-ccd5-e8a0-b333a6fba1a4 -> completed (进度: 100%)
2025-08-15 09:08:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> processing (进度: 0%)
2025-08-15 09:08:08 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 09:08:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 17d9ef9b-7b32-eb8a-9f1c-77fb45d30b7a -> completed (进度: 100%)
2025-08-15 09:08:25 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 488f89a4-4d7b-7754-9a1f-9ae5a366f58d
2025-08-15 09:08:25 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-15 09:08:45 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 488f89a4-4d7b-7754-9a1f-9ae5a366f58d
2025-08-15 09:08:45 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：488f89a4-4d7b-7754-9a1f-9ae5a366f58d
2025-08-15 09:08:57 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-15 09:11:00 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ccabf83b-2610-dee3-b5db-5798e4ce0ec8
2025-08-15 09:11:00 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 09:11:00 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 09:11:00 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 58b48b0d-0866-b6be-7693-0a7753e420e7
2025-08-15 09:11:00 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 09:11:00 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 09:11:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:06 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:07 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:08 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:09 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:11 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:12 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> processing (进度: 0%)
2025-08-15 09:11:12 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 09:11:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:13 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: ccabf83b-2610-dee3-b5db-5798e4ce0ec8 -> completed (进度: 100%)
2025-08-15 09:11:14 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> processing (进度: 0%)
2025-08-15 09:11:14 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 09:11:15 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: 58b48b0d-0866-b6be-7693-0a7753e420e7 -> completed (进度: 100%)
2025-08-15 09:22:56 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a4d2cc89-5d15-60d3-cfc0-7eff1070199d
2025-08-15 09:22:56 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 09:22:56 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 09:22:56 | INFO     | app.core.state_manager:create_task:90 - 创建任务: a6de713a-54c2-faa2-dcce-58df2a969a96
2025-08-15 09:22:56 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-15 09:22:56 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-08-15 09:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:22:57 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:22:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:22:58 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:22:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:22:59 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:23:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:23:00 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:23:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:23:01 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:23:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:23:02 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:23:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> processing (进度: 0%)
2025-08-15 09:23:03 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:23:04 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 09:23:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> processing (进度: 0%)
2025-08-15 09:23:04 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a4d2cc89-5d15-60d3-cfc0-7eff1070199d -> completed (进度: 100%)
2025-08-15 09:23:04 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-08-15 09:23:05 | INFO     | app.api.task:get_task_state:67 - 查询任务状态: a6de713a-54c2-faa2-dcce-58df2a969a96 -> completed (进度: 100%)
2025-08-15 09:24:19 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cf5d94d2-11f9-b013-70bb-1822a963afd5
2025-08-15 09:24:19 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-15 09:24:57 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: cf5d94d2-11f9-b013-70bb-1822a963afd5
2025-08-15 09:24:57 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：cf5d94d2-11f9-b013-70bb-1822a963afd5
2025-08-15 09:25:05 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
