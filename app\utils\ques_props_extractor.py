"""
QuesProps属性提取器
专门用于提取和格式化试题属性信息，为prompt构建提供上下文
"""

from typing import Dict, Any, List, Optional
from loguru import logger


class QuesPropsExtractor:
    """QuesProps属性提取器"""

    def __init__(self):
        self.logger = logger

    def extract_ques_props(self, ques_type_post: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取QuesProps属性信息（修复版本）

        Args:
            ques_type_post: AiQuesTypePost结构数据，包含以下字段：
                - BaseName: 试题基础题型信息
                - QuesTypeName: 试题业务题型信息
                - ChoiceCount: 试题选项数量（选择题时不为0）
                - QuesCount: 需要命制的试题数量
                - QuesProps: 试题属性信息数组

        Returns:
            提取的属性信息字典，按命题信息和题型信息分类
        """
        try:
            extracted_props = {
                # 基础信息
                "base_info": {},           # 基础题型信息
                "question_info": {},       # 题型信息
                "properties": {},          # 基础属性信息
                "detailed_props": {},      # 详细属性信息（包含背景）

                # 分类信息
                "proposition_info": {},    # 命题信息
                "question_type_info": {},  # 题型信息

                # 上下文信息
                "property_context": "",    # 格式化的属性上下文
                "planning_context": "",    # 用于规划阶段的上下文
                "generation_context": "",  # 用于生成阶段的上下文
                "proposition_context": "",  # 命题信息上下文
                "question_type_context": ""  # 题型信息上下文
            }

            # 提取基础信息
            extracted_props["base_info"] = {
                "base_name": ques_type_post.get("BaseName", ""),
                "ques_type_name": ques_type_post.get("QuesTypeName", ""),
                "choice_count": ques_type_post.get("ChoiceCount", 0),
                "ques_count": ques_type_post.get("QuesCount", 1)
            }

            # 提取题型信息
            extracted_props["question_info"] = {
                "base_type": ques_type_post.get("BaseName", ""),
                "business_type": ques_type_post.get("QuesTypeName", ""),
                "is_choice": ques_type_post.get("ChoiceCount", 0) > 0,
                "choice_count": ques_type_post.get("ChoiceCount", 0),
                "question_count": ques_type_post.get("QuesCount", 1)
            }

            # 提取QuesProps属性信息
            ques_props = ques_type_post.get("QuesProps", [])

            for prop in ques_props:
                prop_name = prop.get("QuesPropName", "")
                selected_text = prop.get("SelectQuesPropText", "")
                select_remark = prop.get("SelectQuesPropRemark", "")
                notify_type = prop.get("NotifyType", "")
                value_obj = prop.get("ValueObj", None)

                if prop_name and selected_text:
                    # 基础属性信息
                    extracted_props["properties"][prop_name] = selected_text

                    # 详细属性信息
                    extracted_props["detailed_props"][prop_name] = {
                        "value": selected_text,
                        "remark": select_remark,
                        "notify_type": notify_type,
                        "value_obj": value_obj
                    }

                    # 按类型分类属性
                    self._classify_property(
                        extracted_props, prop_name, selected_text, select_remark, notify_type, value_obj)

            # 生成各种格式化的上下文
            extracted_props["property_context"] = self._format_property_context(
                extracted_props["detailed_props"]
            )
            extracted_props["planning_context"] = self._format_planning_context(
                extracted_props["detailed_props"]
            )
            extracted_props["generation_context"] = self._format_generation_context(
                extracted_props["detailed_props"]
            )
            extracted_props["proposition_context"] = self._format_proposition_context(
                extracted_props["proposition_info"]
            )
            extracted_props["question_type_context"] = self._format_question_type_context(
                extracted_props["question_info"]
            )

            return extracted_props

        except Exception as e:
            self.logger.error(f"提取QuesProps属性失败: {e}")
            return {
                "base_info": {},
                "question_info": {},
                "properties": {},
                "detailed_props": {},
                "proposition_info": {},
                "question_type_info": {},
                "property_context": "",
                "planning_context": "",
                "generation_context": "",
                "proposition_context": "",
                "question_type_context": ""
            }

    def _classify_property(self, extracted_props: Dict[str, Any], prop_name: str,
                           selected_text: str, select_remark: str, notify_type: str, value_obj: Any):
        """
        根据属性名称和内容对属性进行分类

        Args:
            extracted_props: 提取的属性信息字典
            prop_name: 属性名称
            selected_text: 属性值
            select_remark: 属性备注
            notify_type: 通知类型
            value_obj: 值对象
        """
        # 命题信息分类
        proposition_keywords = [
            "难度", "等级", "考查", "要求", "目标", "能力", "素养", "思维", "方法",
            "知识", "考点", "内容", "范围", "重点", "难点", "易错点", "命题素材",
            "背景", "情境", "材料", "主题", "题材", "风格", "特色", "创新"
        ]

        # 题型信息分类
        question_type_keywords = [
            "题型", "类型", "形式", "结构", "格式", "布局", "选项", "答案",
            "解析", "说明", "提示", "引导", "限制", "条件", "规则", "标准"
        ]

        prop_info = {
            "value": selected_text,
            "remark": select_remark,
            "notify_type": notify_type,
            "value_obj": value_obj
        }

        # 判断属性类型
        is_proposition = any(
            keyword in prop_name for keyword in proposition_keywords)
        is_question_type = any(
            keyword in prop_name for keyword in question_type_keywords)

        if is_proposition:
            extracted_props["proposition_info"][prop_name] = prop_info
        elif is_question_type:
            extracted_props["question_type_info"][prop_name] = prop_info
        else:
            # 默认归类为命题信息（因为大部分属性都是命题相关的）
            extracted_props["proposition_info"][prop_name] = prop_info

    def _format_property_context(self, detailed_props: Dict[str, Any]) -> str:
        """
        格式化属性上下文（通用格式）

        Args:
            detailed_props: 详细属性信息

        Returns:
            格式化的属性上下文
        """
        if not detailed_props:
            return "无特殊属性要求"

        context_parts = []
        for prop_name, prop_info in detailed_props.items():
            value = prop_info.get("value", "")
            remark = prop_info.get("remark", "")

            if remark and remark != value:
                context_parts.append(f"- {prop_name}: {value}（{remark}）")
            else:
                context_parts.append(f"- {prop_name}: {value}")

        return "\n".join(context_parts)

    def _format_planning_context(self, detailed_props: Dict[str, Any]) -> str:
        """
        格式化规划阶段上下文（重点突出策略性信息）

        Args:
            detailed_props: 详细属性信息

        Returns:
            格式化的规划上下文
        """
        if not detailed_props:
            return "无特殊规划要求"

        context_parts = []

        # 按属性类型分类处理
        difficulty_info = []
        knowledge_info = []
        requirement_info = []
        other_info = []

        for prop_name, prop_info in detailed_props.items():
            value = prop_info.get("value", "")
            remark = prop_info.get("remark", "")

            # 难度相关
            if "难度" in prop_name or "等级" in prop_name:
                difficulty_info.append(f"{prop_name}: {value}")
                if remark and remark != value:
                    difficulty_info.append(f"  说明: {remark}")

            # 知识点相关
            elif "知识" in prop_name or "考点" in prop_name:
                knowledge_info.append(f"{prop_name}: {value}")
                if remark and remark != value:
                    knowledge_info.append(f"  说明: {remark}")

            # 考查要求相关
            elif "考查" in prop_name or "要求" in prop_name:
                requirement_info.append(f"{prop_name}: {value}")
                if remark and remark != value:
                    requirement_info.append(f"  说明: {remark}")

            # 其他属性
            else:
                other_info.append(f"{prop_name}: {value}")
                if remark and remark != value:
                    other_info.append(f"  说明: {remark}")

        # 组装上下文
        if difficulty_info:
            context_parts.append("难度要求:")
            context_parts.extend(difficulty_info)
            context_parts.append("")

        if knowledge_info:
            context_parts.append("知识点要求:")
            context_parts.extend(knowledge_info)
            context_parts.append("")

        if requirement_info:
            context_parts.append("考查要求:")
            context_parts.extend(requirement_info)
            context_parts.append("")

        if other_info:
            context_parts.append("其他属性:")
            context_parts.extend(other_info)

        return "\n".join(context_parts).strip()

    def _format_generation_context(self, detailed_props: Dict[str, Any]) -> str:
        """
        格式化生成阶段上下文（重点突出具体实现要求）

        Args:
            detailed_props: 详细属性信息

        Returns:
            格式化的生成上下文
        """
        if not detailed_props:
            return "无特殊生成要求"

        context_parts = []

        for prop_name, prop_info in detailed_props.items():
            value = prop_info.get("value", "")
            remark = prop_info.get("remark", "")

            # 生成阶段重点关注具体实现要求
            if remark and remark != value:
                context_parts.append(f"• {prop_name}: {value}")
                context_parts.append(f"  具体要求: {remark}")
            else:
                context_parts.append(f"• {prop_name}: {value}")

        return "\n".join(context_parts)

    def _format_proposition_context(self, proposition_info: Dict[str, Any]) -> str:
        """
        格式化命题信息上下文

        Args:
            proposition_info: 命题信息

        Returns:
            格式化的命题信息上下文
        """
        if not proposition_info:
            return "无特殊命题要求"

        context_parts = []

        # 按重要性分类
        difficulty_info = []
        knowledge_info = []
        requirement_info = []
        material_info = []
        other_info = []

        for prop_name, prop_info in proposition_info.items():
            value = prop_info.get("value", "")
            remark = prop_info.get("remark", "")

            # 难度相关
            if "难度" in prop_name or "等级" in prop_name:
                difficulty_info.append(f"• {prop_name}: {value}")
                if remark and remark != value:
                    difficulty_info.append(f"  补充说明: {remark}")

            # 知识点相关
            elif "知识" in prop_name or "考点" in prop_name or "内容" in prop_name:
                knowledge_info.append(f"• {prop_name}: {value}")
                if remark and remark != value:
                    knowledge_info.append(f"  补充说明: {remark}")

            # 考查要求相关
            elif "考查" in prop_name or "要求" in prop_name or "目标" in prop_name:
                requirement_info.append(f"• {prop_name}: {value}")
                if remark and remark != value:
                    requirement_info.append(f"  补充说明: {remark}")

            # 命题素材相关
            elif "素材" in prop_name or "背景" in prop_name or "情境" in prop_name:
                material_info.append(f"• {prop_name}: {value}")
                if remark and remark != value:
                    material_info.append(f"  补充说明: {remark}")

            # 其他属性
            else:
                other_info.append(f"• {prop_name}: {value}")
                if remark and remark != value:
                    other_info.append(f"  补充说明: {remark}")

        # 组装上下文
        if difficulty_info:
            context_parts.append("【难度要求】")
            context_parts.extend(difficulty_info)
            context_parts.append("")

        if knowledge_info:
            context_parts.append("【知识点要求】")
            context_parts.extend(knowledge_info)
            context_parts.append("")

        if requirement_info:
            context_parts.append("【考查要求】")
            context_parts.extend(requirement_info)
            context_parts.append("")

        if material_info:
            context_parts.append("【命题素材】")
            context_parts.extend(material_info)
            context_parts.append("")

        if other_info:
            context_parts.append("【其他要求】")
            context_parts.extend(other_info)

        return "\n".join(context_parts).strip()

    def _format_question_type_context(self, question_info: Dict[str, Any]) -> str:
        """
        格式化题型信息上下文

        Args:
            question_info: 题型信息

        Returns:
            格式化的题型信息上下文
        """
        if not question_info:
            return "无特殊题型要求"

        context_parts = []

        # 基础题型信息
        base_type = question_info.get("base_type", "")
        business_type = question_info.get("business_type", "")
        is_choice = question_info.get("is_choice", False)
        choice_count = question_info.get("choice_count", 0)
        question_count = question_info.get("question_count", 1)

        context_parts.append("【题型信息】")
        context_parts.append(f"• 基础题型: {base_type}")
        context_parts.append(f"• 业务题型: {business_type}")
        context_parts.append(f"• 是否选择题: {'是' if is_choice else '否'}")

        if is_choice:
            context_parts.append(f"• 选项数量: {choice_count}")

        context_parts.append(f"• 题目数量: {question_count}")

        return "\n".join(context_parts).strip()

    def get_property_by_name(self, detailed_props: Dict[str, Any], prop_name: str) -> Optional[Dict[str, Any]]:
        """
        根据属性名称获取特定属性信息

        Args:
            detailed_props: 详细属性信息
            prop_name: 属性名称

        Returns:
            属性信息字典，如果不存在返回None
        """
        return detailed_props.get(prop_name)

    def get_property_value(self, detailed_props: Dict[str, Any], prop_name: str) -> Optional[str]:
        """
        根据属性名称获取属性值

        Args:
            detailed_props: 详细属性信息
            prop_name: 属性名称

        Returns:
            属性值，如果不存在返回None
        """
        prop_info = detailed_props.get(prop_name)
        return prop_info.get("value") if prop_info else None

    def get_property_remark(self, detailed_props: Dict[str, Any], prop_name: str) -> Optional[str]:
        """
        根据属性名称获取属性备注

        Args:
            detailed_props: 详细属性信息
            prop_name: 属性名称

        Returns:
            属性备注，如果不存在返回None
        """
        prop_info = detailed_props.get(prop_name)
        return prop_info.get("remark") if prop_info else None

    def has_property(self, detailed_props: Dict[str, Any], prop_name: str) -> bool:
        """
        检查是否存在指定属性

        Args:
            detailed_props: 详细属性信息
            prop_name: 属性名称

        Returns:
            是否存在该属性
        """
        return prop_name in detailed_props


# 全局实例
ques_props_extractor = QuesPropsExtractor()
