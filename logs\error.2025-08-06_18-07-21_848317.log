2025-08-06 18:07:21 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:21 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:21 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:43 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:43 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:43 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:45 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:45 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:45 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:18:03 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:21:20 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:32 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:32 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:32 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:23:34 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:27 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:27 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:27 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:31:29 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:34:37 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Connection error.
2025-08-06 18:34:37 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-06 18:34:37 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Connection error.
2025-08-06 18:34:39 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Connection error.
2025-08-06 18:34:39 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-06 18:34:39 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:34:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:34:50 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Connection error.
