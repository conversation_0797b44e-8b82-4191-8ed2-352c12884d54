2025-08-02 15:57:27 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 74892a4b-b292-0164-5a15-453c80fca635
2025-08-02 15:57:27 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-02 15:58:31 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 74892a4b-b292-0164-5a15-453c80fca635
2025-08-02 15:58:31 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：74892a4b-b292-0164-5a15-453c80fca635
2025-08-02 15:58:47 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-02 16:03:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: de2c332f-fc52-28e3-bccc-862a7363c1d6
2025-08-02 16:03:35 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-02 16:04:51 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: de2c332f-fc52-28e3-bccc-862a7363c1d6
2025-08-02 16:04:51 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：de2c332f-fc52-28e3-bccc-862a7363c1d6
2025-08-02 16:06:20 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-02 16:08:34 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f8387f44-6f18-79e4-87ca-c52749ab3df7
2025-08-02 16:08:34 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-02 16:09:18 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: f8387f44-6f18-79e4-87ca-c52749ab3df7
2025-08-02 16:09:18 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f8387f44-6f18-79e4-87ca-c52749ab3df7
2025-08-02 16:10:05 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-02 16:27:30 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-02 16:27:30 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-02 16:39:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 365c21ba-e068-209d-7570-d33704142835
2025-08-02 16:39:30 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-02 16:39:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: deepseek-reasoner
2025-08-02 16:40:38 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 365c21ba-e068-209d-7570-d33704142835
2025-08-02 16:40:38 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：365c21ba-e068-209d-7570-d33704142835
2025-08-02 16:41:26 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-02 16:44:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2939c520-6af8-8f48-4908-a598d3f72633
2025-08-02 16:44:35 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-02 16:45:25 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 2939c520-6af8-8f48-4908-a598d3f72633
2025-08-02 16:45:25 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：2939c520-6af8-8f48-4908-a598d3f72633
2025-08-02 16:46:47 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
