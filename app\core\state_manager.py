"""
任务状态管理模块
实现全局状态管理和流式输出队列
"""

from enum import Enum
from typing import Dict, Optional, List, Any
from dataclasses import dataclass
import asyncio
import time
import uuid
from loguru import logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败


# 删除BaseVar类，不再使用


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    status: TaskStatus
    created_at: float
    updated_at: float
    result: Optional[Dict] = None
    error_message: Optional[str] = None
    stream_queue: Optional[asyncio.Queue] = None
    is_completed: bool = False  # 流式输出是否完成
    current_step: Optional[str] = None  # 当前执行步骤
    progress: int = 0  # 当前进度百分比
    step_message: Optional[str] = None  # 当前步骤描述
    step_data: Optional[Dict] = None  # 当前步骤详细数据


class StreamContext:
    """流式连接上下文管理器"""

    def __init__(self):
        self._active_connections: Dict[str, bool] = {}
        self._lock = asyncio.Lock()

    async def set_connection_active(self, task_id: str, active: bool):
        """设置连接状态"""
        async with self._lock:
            self._active_connections[task_id] = active
            logger.debug(f"设置连接状态: {task_id} -> {active}")  # 改为DEBUG级别

    async def is_connection_active(self, task_id: str) -> bool:
        """检查连接是否活跃"""
        async with self._lock:
            return self._active_connections.get(task_id, False)

    async def remove_connection(self, task_id: str):
        """移除连接"""
        async with self._lock:
            if task_id in self._active_connections:
                del self._active_connections[task_id]
                logger.debug(f"移除连接: {task_id}")  # 改为DEBUG级别


class TaskStateManager:
    """全局任务状态管理器"""

    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
        self._lock = asyncio.Lock()
        self.stream_context = StreamContext()
        logger.info("任务状态管理器初始化完成")

    async def create_task(self, task_id: str) -> TaskInfo:
        """创建新任务"""
        async with self._lock:
            task_info = TaskInfo(
                task_id=task_id,
                status=TaskStatus.PENDING,
                created_at=time.time(),
                updated_at=time.time(),
                stream_queue=asyncio.Queue(maxsize=2000),  # 增加队列大小，提高性能
                is_completed=False
            )
            self._tasks[task_id] = task_info
            logger.info(f"创建任务: {task_id}")  # 保留关键业务日志
            return task_info

    async def update_task_status(self, task_id: str, status: TaskStatus,
                                 result: Optional[Dict] = None,
                                 error_message: Optional[str] = None,
                                 step_info: Optional[Dict] = None):
        """更新任务状态"""
        async with self._lock:
            if task_id in self._tasks:
                self._tasks[task_id].status = status
                self._tasks[task_id].updated_at = time.time()
                if result is not None:
                    self._tasks[task_id].result = result
                if error_message is not None:
                    self._tasks[task_id].error_message = error_message
                if status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    self._tasks[task_id].is_completed = True

                # 更新步骤信息
                if step_info:
                    self._tasks[task_id].current_step = step_info.get("step")
                    self._tasks[task_id].progress = step_info.get(
                        "progress", 0)
                    self._tasks[task_id].step_message = step_info.get(
                        "message")
                    self._tasks[task_id].step_data = step_info

                logger.debug(  # 改为DEBUG级别
                    f"更新任务状态: {task_id} -> {status.value} (进度: {self._tasks[task_id].progress}%)")
            else:
                logger.warning(f"任务不存在: {task_id}")

    async def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        async with self._lock:
            return self._tasks.get(task_id)

    # 删除BaseVar相关方法，不再使用

    async def add_stream_data(self, task_id: str, data: Dict[str, Any]):
        """添加流式数据到队列 - 优化版本"""
        # 减少锁的使用，提高性能
        if task_id in self._tasks and self._tasks[task_id].stream_queue:
            try:
                # 使用非阻塞方式添加数据，避免队列满时阻塞
                self._tasks[task_id].stream_queue.put_nowait(data)
                logger.debug(f"添加流式数据到任务: {task_id}")
            except asyncio.QueueFull:
                # 队列满时，移除最旧的数据，添加新数据
                try:
                    self._tasks[task_id].stream_queue.get_nowait()
                    self._tasks[task_id].stream_queue.put_nowait(data)
                    logger.warning(f"队列满，移除旧数据后添加新数据: {task_id}")
                except asyncio.QueueEmpty:
                    # 如果队列为空，直接添加
                    self._tasks[task_id].stream_queue.put_nowait(data)
            except Exception as e:
                logger.error(f"添加流式数据失败: {task_id}, 错误: {e}")
        else:
            logger.warning(f"任务不存在或无流式队列: {task_id}")

    async def get_stream_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取流式数据"""
        async with self._lock:
            if task_id in self._tasks and self._tasks[task_id].stream_queue:
                try:
                    return self._tasks[task_id].stream_queue.get_nowait()
                except asyncio.QueueEmpty:
                    return None
            return None

    async def mark_stream_completed(self, task_id: str):
        """标记流式输出完成"""
        async with self._lock:
            if task_id in self._tasks:
                self._tasks[task_id].is_completed = True
                # 添加完成标记到队列
                if self._tasks[task_id].stream_queue:
                    try:
                        # 等待一小段时间确保所有数据都已推送
                        await asyncio.sleep(0.05)
                        self._tasks[task_id].stream_queue.put_nowait(
                            False)  # 完成标记
                        logger.debug(f"添加完成标记到队列: {task_id}")  # 改为DEBUG级别
                    except asyncio.QueueFull:
                        # 队列满时，清空队列后添加完成标记
                        while not self._tasks[task_id].stream_queue.empty():
                            try:
                                self._tasks[task_id].stream_queue.get_nowait()
                            except asyncio.QueueEmpty:
                                break
                        self._tasks[task_id].stream_queue.put_nowait(False)
                        logger.debug(f"清空队列后添加完成标记: {task_id}")  # 改为DEBUG级别
                logger.debug(f"标记流式输出完成: {task_id}")  # 改为DEBUG级别

    async def cleanup_task(self, task_id: str):
        """清理任务数据（请求结束后调用）"""
        async with self._lock:
            if task_id in self._tasks:
                del self._tasks[task_id]
                await self.stream_context.remove_connection(task_id)
                logger.debug(f"清理任务数据: {task_id}")  # 改为DEBUG级别

    async def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """获取所有任务信息（用于调试）"""
        async with self._lock:
            return self._tasks.copy()


# 全局实例
task_manager = TaskStateManager()


# 流式数据类型定义
STREAM_TYPES = {
    "THINKING": "LLM推理思考过程"
}


def create_stream_data(stream_type: str, message: str, **kwargs) -> Dict[str, Any]:
    """创建标准流式数据格式"""
    return {
        "type": stream_type,
        "timestamp": time.time(),
        "message": message,
        **kwargs
    }


# 全局变量管理函数，参考用户设计
# 删除未使用的全局函数
