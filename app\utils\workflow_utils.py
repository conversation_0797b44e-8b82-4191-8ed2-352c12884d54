"""
工作流工具函数
用于从WorkflowPost中提取模型配置和其他信息
"""

from typing import Dict, Any, Optional, List
from loguru import logger


def extract_model_config_from_workflow(workflow_post: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从WorkflowPost中提取模型配置

    Args:
        workflow_post: WorkflowPost字典数据

    Returns:
        模型配置字典，包含模型名称和参数
    """
    try:
        if not workflow_post or "NodeList" not in workflow_post:
            logger.warning("WorkflowPost数据为空或缺少NodeList")
            return None

        node_list = workflow_post["NodeList"]
        if not node_list:
            logger.warning("NodeList为空")
            return None

        # 遍历所有节点，查找第一个有模型配置的节点
        for node in node_list:
            if "NodeContent" in node and node["NodeContent"]:
                for content in node["NodeContent"]:
                    if "NodeModel" in content and content["NodeModel"]:
                        node_model = content["NodeModel"]

                        # 构建模型配置
                        model_config = {
                            "name": node_model.get("ModelName", "deepseek-r1"),
                            "model_name": node_model.get("ModelName", "deepseek-r1"),
                            "api_key": node_model.get("ModelArgs", {}).get("ApiKey", ""),
                            "api_base": node_model.get("ModelArgs", {}).get("ChatUrl", ""),
                            "temperature": float(node_model.get("ModelArgs", {}).get("Temperature", "0.7")),
                            "max_tokens": int(node_model.get("ModelArgs", {}).get("MaxToken", "2048")),
                            "top_p": float(node_model.get("ModelArgs", {}).get("TopP", "0.5")),
                            "top_k": int(node_model.get("ModelArgs", {}).get("TopK", "5"))
                        }

                        logger.info(
                            f"从WorkflowPost提取到模型配置: {model_config['name']}")
                        return model_config

        logger.warning("未在WorkflowPost中找到有效的模型配置")
        return None

    except Exception as e:
        logger.error(f"提取模型配置时出错: {e}")
        return None


def extract_prompt_from_workflow(workflow_post: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从WorkflowPost中提取提示词配置

    Args:
        workflow_post: WorkflowPost字典数据

    Returns:
        提示词配置字典
    """
    try:
        if not workflow_post or "NodeList" not in workflow_post:
            return None

        node_list = workflow_post["NodeList"]
        if not node_list:
            return None

        # 遍历所有节点，查找第一个有提示词配置的节点
        for node in node_list:
            if "NodeContent" in node and node["NodeContent"]:
                for content in node["NodeContent"]:
                    if "NodePrompt" in content and content["NodePrompt"]:
                        node_prompt = content["NodePrompt"]

                        # 构建提示词配置
                        prompt_config = {
                            "function": node_prompt.get("功能", ""),
                            "workflow": node_prompt.get("工作流", ""),
                            "requirement": node_prompt.get("要求", ""),
                            "overall_task": node_prompt.get("总体任务", ""),
                            "overall_requirement": node_prompt.get("总体要求", "")
                        }

                        logger.info("从WorkflowPost提取到提示词配置")
                        return prompt_config

        return None

    except Exception as e:
        logger.error(f"提取提示词配置时出错: {e}")
        return None


def get_workflow_node_names(workflow_post: Dict[str, Any]) -> List[str]:
    """
    获取工作流节点名称列表

    Args:
        workflow_post: WorkflowPost字典数据

    Returns:
        节点名称列表
    """
    try:
        if not workflow_post or "NodeList" not in workflow_post:
            return []

        node_list = workflow_post["NodeList"]
        return [node.get("NodeName", "") for node in node_list if node.get("NodeName")]

    except Exception as e:
        logger.error(f"获取节点名称时出错: {e}")
        return []


def validate_workflow_config(workflow_post: Dict[str, Any]) -> bool:
    """
    验证WorkflowPost配置的有效性

    Args:
        workflow_post: WorkflowPost字典数据

    Returns:
        配置是否有效
    """
    try:
        if not workflow_post:
            return False

        if "NodeList" not in workflow_post:
            return False

        node_list = workflow_post["NodeList"]
        if not node_list:
            return False

        # 检查是否至少有一个节点包含模型配置
        for node in node_list:
            if "NodeContent" in node and node["NodeContent"]:
                for content in node["NodeContent"]:
                    if "NodeModel" in content and content["NodeModel"]:
                        return True

        return False

    except Exception as e:
        logger.error(f"验证工作流配置时出错: {e}")
        return False
