2025-08-04 08:31:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8a197b9d-12f3-5f0b-0727-979105f4deb7
2025-08-04 08:31:53 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-04 08:31:53 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-04 08:32:38 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8a197b9d-12f3-5f0b-0727-979105f4deb7
2025-08-04 08:32:38 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8a197b9d-12f3-5f0b-0727-979105f4deb7
2025-08-04 08:34:22 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-04 08:36:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: c491397e-9622-775d-a671-b75ab3366190
2025-08-04 08:36:04 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-04 08:36:38 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: c491397e-9622-775d-a671-b75ab3366190
2025-08-04 08:36:38 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：c491397e-9622-775d-a671-b75ab3366190
2025-08-04 08:37:13 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
