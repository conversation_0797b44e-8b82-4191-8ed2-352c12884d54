"""
试题信息解析工具
专门用于解析AiQuesTypePost的递归数据结构
"""

from typing import Dict, List, Any, Optional, Union
from app.models.schemas import QuesTypePost, QuesProp, Element
from loguru import logger


class QuestionParser:
    """试题信息解析器"""

    def __init__(self):
        self.parsed_questions = []
        self.question_counter = 0

    def parse_question_structure(self, ques_type_post: Union[Dict, QuesTypePost]) -> Dict[str, Any]:
        """
        解析试题结构信息

        Args:
            ques_type_post: AiQuesTypePost数据（字典或Pydantic模型）

        Returns:
            解析后的试题结构信息
        """
        try:
            # 转换为字典格式
            if isinstance(ques_type_post, QuesTypePost):
                data = ques_type_post.dict()
            else:
                data = ques_type_post

            # 重置解析状态
            self.parsed_questions = []
            self.question_counter = 0

            # 开始递归解析
            result = self._parse_recursive(data, level=0)

            return {
                "question_structure": result,
                "total_questions": self.question_counter,
                "question_list": self.parsed_questions,
                "summary": self._generate_summary(result)
            }

        except Exception as e:
            logger.error(f"解析试题结构失败: {e}")
            raise ValueError(f"试题结构解析错误: {str(e)}")

    def _parse_recursive(self, data: Dict[str, Any], level: int = 0) -> Dict[str, Any]:
        """
        递归解析试题数据

        Args:
            data: 当前层级的试题数据
            level: 当前层级深度

        Returns:
            解析后的试题信息
        """
        result = {
            "level": level,
            "question_id": f"Q{self.question_counter + 1}",
            "base_name": data.get("BaseName", ""),
            "ques_type_id": data.get("QuesTypeId", ""),
            "ques_type_name": data.get("QuesTypeName", ""),
            "choice_count": data.get("ChoiceCount", 0),
            "ques_count": data.get("QuesCount", 1),
            "ques_str": data.get("QuesStr", ""),
            "elements": self._parse_elements(data.get("Elements", [])),
            "properties": self._parse_properties(data.get("QuesProps", [])),
            "children": [],
            "prompt_info": data.get("QuesPrompt", {})
        }

        # 增加题目计数
        self.question_counter += 1

        # 添加到题目列表
        self.parsed_questions.append({
            "question_id": result["question_id"],
            "level": level,
            "type_name": result["ques_type_name"],
            "base_name": result["base_name"],
            "properties": result["properties"]
        })

        # 递归处理子题目
        children = data.get("Childs", [])
        for child in children:
            child_result = self._parse_recursive(child, level + 1)
            result["children"].append(child_result)

        return result

    def _parse_elements(self, elements: List[Dict]) -> List[Dict[str, Any]]:
        """
        解析题目元素

        Args:
            elements: 元素列表

        Returns:
            解析后的元素信息
        """
        parsed_elements = []

        for element in elements:
            parsed_element = {
                "element_type": element.get("ElementType", ""),
                "element_text": element.get("ElementText", []),
                "element_count": len(element.get("ElementText", [])),
                "has_content": bool(element.get("ElementText"))
            }
            parsed_elements.append(parsed_element)

        return parsed_elements

    def _parse_properties(self, properties: List[Dict]) -> Dict[str, Any]:
        """
        解析题目属性

        Args:
            properties: 属性列表

        Returns:
            解析后的属性信息
        """
        parsed_props = {}

        for prop in properties:
            prop_name = prop.get("QuesPropName", "")
            prop_id = prop.get("QuesPropId", "")

            # 解析属性来源
            sources = []
            for source in prop.get("QuesPropSource", []):
                sources.append({
                    "id": source.get("Id", ""),
                    "text": source.get("text", ""),
                    "remark": source.get("remark", "")
                })

            # 解析属性数组
            prop_array = prop.get("QuesPropArr", [])

            parsed_props[prop_name] = {
                "prop_id": prop_id,
                "sources": sources,
                "options": prop_array,
                "selected": prop.get("SelectQuesPropText", ""),
                "notify_type": prop.get("NotifyType", ""),
                "remark": prop.get("SelectQuesPropRemark", ""),
                "value_obj": prop.get("ValueObj")
            }

        return parsed_props

    def _generate_summary(self, question_structure: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成试题结构摘要

        Args:
            question_structure: 解析后的试题结构

        Returns:
            摘要信息
        """
        def count_questions_recursive(data: Dict[str, Any]) -> int:
            count = 1  # 当前题目
            for child in data.get("children", []):
                count += count_questions_recursive(child)
            return count

        def get_max_depth_recursive(data: Dict[str, Any], current_depth: int = 0) -> int:
            max_depth = current_depth
            for child in data.get("children", []):
                child_depth = get_max_depth_recursive(child, current_depth + 1)
                max_depth = max(max_depth, child_depth)
            return max_depth

        total_questions = count_questions_recursive(question_structure)
        max_depth = get_max_depth_recursive(question_structure)

        return {
            "total_questions": total_questions,
            "max_depth": max_depth,
            "main_type": question_structure.get("ques_type_name", ""),
            "has_children": bool(question_structure.get("children")),
            "element_types": list(set(
                elem.get("element_type", "")
                for elem in question_structure.get("elements", [])
            )),
            "property_names": list(question_structure.get("properties", {}).keys())
        }

    def extract_question_prompt(self, ques_type_post: Union[Dict, QuesTypePost]) -> str:
        """
        提取试题命制提示词

        Args:
            ques_type_post: AiQuesTypePost数据

        Returns:
            组合后的提示词
        """
        try:
            structure = self.parse_question_structure(ques_type_post)

            prompt_parts = []

            # 添加基础信息
            main_question = structure["question_structure"]
            prompt_parts.append(f"题目类型: {main_question['ques_type_name']}")
            prompt_parts.append(f"基础名称: {main_question['base_name']}")

            # 添加属性信息
            properties = main_question.get("properties", {})
            for prop_name, prop_info in properties.items():
                if prop_info.get("selected"):
                    prompt_parts.append(
                        f"{prop_name}: {prop_info['selected']}")
                elif prop_info.get("options"):
                    prompt_parts.append(
                        f"{prop_name}选项: {', '.join(prop_info['options'])}")

            # 添加元素信息
            elements = main_question.get("elements", [])
            for element in elements:
                if element.get("element_text"):
                    prompt_parts.append(
                        f"{element['element_type']}: {', '.join(element['element_text'])}")

            # 添加子题目信息
            if main_question.get("children"):
                prompt_parts.append(
                    f"包含 {len(main_question['children'])} 个子题目")
                for i, child in enumerate(main_question["children"], 1):
                    prompt_parts.append(f"子题{i}: {child['ques_type_name']}")

            return "\n".join(prompt_parts)

        except Exception as e:
            logger.error(f"提取提示词失败: {e}")
            return "试题信息解析失败"

    def get_question_hierarchy(self, ques_type_post: Union[Dict, QuesTypePost]) -> List[Dict[str, Any]]:
        """
        获取试题层级结构

        Args:
            ques_type_post: AiQuesTypePost数据

        Returns:
            层级结构列表
        """
        try:
            structure = self.parse_question_structure(ques_type_post)

            def build_hierarchy(data: Dict[str, Any], level: int = 0) -> List[Dict[str, Any]]:
                hierarchy = [{
                    "level": level,
                    "question_id": data["question_id"],
                    "type_name": data["ques_type_name"],
                    "base_name": data["base_name"],
                    "has_children": bool(data.get("children"))
                }]

                for child in data.get("children", []):
                    hierarchy.extend(build_hierarchy(child, level + 1))

                return hierarchy

            return build_hierarchy(structure["question_structure"])

        except Exception as e:
            logger.error(f"获取层级结构失败: {e}")
            return []


# 全局实例
question_parser = QuestionParser()


def parse_question_structure(ques_type_post: Union[Dict, QuesTypePost]) -> Dict[str, Any]:
    """
    便捷函数：解析试题结构

    Args:
        ques_type_post: AiQuesTypePost数据

    Returns:
        解析结果
    """
    return question_parser.parse_question_structure(ques_type_post)


def extract_question_prompt(ques_type_post: Union[Dict, QuesTypePost]) -> str:
    """
    便捷函数：提取试题提示词

    Args:
        ques_type_post: AiQuesTypePost数据

    Returns:
        提示词字符串
    """
    return question_parser.extract_question_prompt(ques_type_post)


def get_question_hierarchy(ques_type_post: Union[Dict, QuesTypePost]) -> List[Dict[str, Any]]:
    """
    便捷函数：获取试题层级

    Args:
        ques_type_post: AiQuesTypePost数据

    Returns:
        层级结构列表
    """
    return question_parser.get_question_hierarchy(ques_type_post)
