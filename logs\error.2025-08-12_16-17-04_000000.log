2025-08-12 11:43:18 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:18 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:18 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:19 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:19 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:19 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:21 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:22 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 11:43:22 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:52:25 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:26 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:27 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 11:52:27 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 13:35:38 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:39 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:40 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 13:35:40 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:41:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:45 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:47 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 13:41:47 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:57:46 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:57:46 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:57:46 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:37 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:37 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:37 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-12 13:58:39 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 13:58:48 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-12 14:08:36 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:21:25 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:30:08 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:31:59 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 15:08:43 | ERROR    | app.services.llm_manager:_call_generic_stream_api:547 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:43 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:43 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:544 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:547 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:544 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:_call_generic_stream_api:547 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:544 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划最终失败，已重试 3 次
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1289 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:770 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:770 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:770 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1673 - 试题1生成最终失败，已重试3次
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1628 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 16:17:04 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1609 - 试题1生成最终失败，已重试3次
2025-08-12 16:17:04 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1564 - 试题1生成最终失败: 'ConfigManager' object is not subscriptable
