"""
智能命题系统主应用
"""

from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
import sys
from app.api import agent, task, stream

# 配置日志 - 生产环境优化版本
import os

# 获取环境变量，默认为生产环境
ENVIRONMENT = os.getenv("ENVIRONMENT", "production")

logger.remove()

if ENVIRONMENT == "development":
    # 开发环境：详细日志输出到控制台
    logger.add(
        sys.stdout,
        level="DEBUG",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    logger.add(
        "logs/app.log",
        level="DEBUG",
        rotation="1 day",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    )
else:
    # 生产环境：只输出WARNING及以上级别到控制台，INFO及以上级别到文件
    logger.add(
        sys.stdout,
        level="WARNING",  # 控制台只显示警告和错误
        format="<red>{time:YYYY-MM-DD HH:mm:ss}</red> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    logger.add(
        "logs/app.log",
        level="INFO",  # 文件记录INFO及以上级别
        rotation="1 day",
        retention="30 days",  # 生产环境保留更长时间
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    )
    # 添加错误日志单独文件
    logger.add(
        "logs/error.log",
        level="ERROR",
        rotation="1 day",
        retention="90 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    )


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("智能命题系统启动中...")
    logger.info("系统初始化完成")

    yield

    # 关闭时执行
    logger.info("智能命题系统关闭中...")


# 创建FastAPI应用
app = FastAPI(
    title="智能命题系统",
    description="基于LangChain和LangGraph的智能命题、智能克隆、模型交互系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该指定具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理器


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "code": 3001,
            "message": "服务器内部错误",
            "details": str(exc),
            "timestamp": "2024-01-01T12:00:00Z"
        }
    )

# 健康检查接口


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "智能命题系统运行正常"}

# 注册路由
# app.include_router(agent.router, prefix="/api/v1", tags=["智能代理"])
# app.include_router(task.router, prefix="/api/v1", tags=["任务管理"])
# app.include_router(stream.router, prefix="/api/v1", tags=["流式输出"])
app.include_router(agent.router, tags=["智能代理"])
app.include_router(task.router, tags=["任务管理"])
app.include_router(stream.router, tags=["流式输出"])

if __name__ == "__main__":
    uvicorn.run(
        app=app,
        host="0.0.0.0",
        port=7861,  # 7861 8000
        reload=False,
        log_level="info"
    )
