2025-08-01 11:33:09 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:33:09 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:33:09 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:33:11 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:33:11 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:33:11 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:33:13 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:33:13 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:33:16 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:33:19 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:33:22 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:33:22 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:34:02 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:34:02 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:34:02 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:34:04 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:34:04 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:34:04 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:34:07 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:34:07 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:34:09 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:34:12 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:34:16 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:34:16 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:39:13 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:13 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:13 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:15 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:15 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:15 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:17 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:39:17 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:20 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:23 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:27 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:39:27 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:39:48 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:48 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:48 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:50 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:50 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:50 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Connection error.
2025-08-01 11:39:52 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:39:52 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:55 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:39:58 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Connection error.
2025-08-01 11:40:02 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 11:40:02 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Connection error.
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:44:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:44:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Requested token count exceeds the model's maximum context length of 8192 tokens. You requested a total of 9059 tokens: 5059 tokens from the input messages and 4000 tokens for the completion. Please reduce the number of tokens in the input messages or the completion to fit within the limit.
2025-08-01 11:53:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 11:53:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: 解析结果验证失败
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '467bf0cdae1f358cc520a5771e3d6280', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:24 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '14b76a7fe8b967a20271f8b5d24f0516', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:25 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 17:38:25 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Error code: 401 - {'id': '2bcb1058e1a7c253f2b4384d0c66fd31', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '9790794546d1b938775becfb7912f9e7', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:26 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '5688332718cc29a94fccf313be5165c0', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:38:27 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 17:38:27 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Error code: 401 - {'id': 'c726e9e94d382b34bb9934a869bee89c', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:35 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '0dcebc802637187eb44889bdf5428892', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:36 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:36 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:36 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': 'd13c14fdaf38c5f0ada69911ecb05583', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_generic_stream_api:529 - 通用流式API调用失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: Error code: 401 - {'id': '27418b1f1ba10cbe094897c6ec68df61', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '8ccc8d9c4137fda7a2b15582562f1b18', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:37 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'dd2c3fa7400eca9a64786fe7c913963b', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:40:38 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 17:40:38 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Error code: 401 - {'id': 'ac721b5663fc1a8313237cbc52a1b3f1', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:05 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"0c095b7b26564a029e4c1a25a7017560","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"0c095b7b26564a029e4c1a25a7017560","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"300c116ac244d869870e4807e8496264","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:05 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"300c116ac244d869870e4807e8496264","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:06 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 17:53:06 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: DeepSeek流式API调用失败: 401 - {"id":"c884bf98750cb80a63914e201acfa6c2","error":{"message":"not authorized","type":"not_authorized_error","param":null,"code":"not_authorized"}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': '50305a841a041d3045e7d3089190f1b2', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:07 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'cc04a02abf88dad6fbbd572946c2b0d4', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:766 - LLM非流式调用失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 17:53:08 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1669 - 试题1生成最终失败，已重试3次
2025-08-01 17:53:08 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1624 - 试题1生成最终失败: Error code: 401 - {'id': 'f736f2609fa9cb1f97ce1f4aa6fc4d75', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 18:11:34 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:34 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:38 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:38 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:11:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 18:11:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:54 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:54 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:58 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:13:58 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:14:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 18:14:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: can only concatenate str (not "NoneType") to str
2025-08-01 18:15:47 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:15:47 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:17:05 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:17:05 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:22 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:22 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:26 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:26 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: can only concatenate str (not "NoneType") to str
2025-08-01 18:22:30 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1281 - 命题规划最终失败，已重试 3 次
2025-08-01 18:22:30 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划重试失败，使用默认规划: can only concatenate str (not "NoneType") to str
2025-08-01 18:40:32 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: 
2025-08-01 18:40:32 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:541 - LLM流式调用失败: 
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.question_cloner:_generate_cloned_content:816 - LLM生成内容失败: Error code: 401 - {'id': '121205e2a17c7125d5032f9e4f56a7bd', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.question_cloner:_generate_cloned_content:816 - LLM生成内容失败: Error code: 401 - {'id': '808ff41b02cbf470aedb00db89c8768e', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
2025-08-01 19:13:43 | ERROR    | app.services.question_cloner:_generate_cloned_content:816 - LLM生成内容失败: Error code: 401 - {'id': 'be2e700a8ccbb510bb34e14187b31e1d', 'error': {'message': 'not authorized', 'type': 'not_authorized_error', 'param': None, 'code': 'not_authorized'}}
