# 项目系统设计
## 主要功能点
业务：智能命题、智能克隆、模型交互
系统：进度查询（轮询）、 请求管理、 模型命题过程的流式返回接口

## 技术栈
1. 后端：python + fastapi
2. 模型交互 + workflow： langchain + langgraph


模型接口设计：

### 1. /AgentGen
- **方法**：POST
- **功能**：根据结构化参数，完成命题/克隆/LLM交互等主业务
- **入参**：直接采用传入结构化JSON（QuesPost、WorkflowPost、AgentPost、TaskName、TaskId等）
  - 参数解读：QuesPost 试题业务信息 ；WorkflowPost 模型信息 （包含工作流信息，暂时不用） ；AgentPost（任务信息，设定角色信息等） ；TaskName 任务名称 ；TaskId 任务ID
  - /AgentGen传入参数示例：
```json
{
    "QuesPost": { // 试题业务信息   智能命题\试题克隆 使用
        "ExamProjectName": "湖北省学考合格考命审题与题库管理系统",
        "ExamSubjectName": "语文",
        "AiAssignSupplement": "{\"命题知识点\":\"项脊轩志\"}",
        "AiQuesTypePost": {
            "QuesPrompt": {},
            "Elements": [
                {
                    "ElementType": "langMaterial",
                    "ElementText": []
                }
            ],
            "BaseName": "组合题",
            "QuesTypeId": "992504162017020080002443632807",
            "QuesTypeName": "组合题",
            "ChoiceCount": 0,
            "QuesCount": 1,
            "QuesStr": null,
            "QuesProps": [
                {
                    "QuesPropName": "必备知识",
                    "QuesPropId": "992502191515283100003958726989",
                    "QuesPropSource": [
                        {
                            "Id": "992503071613473570010247655701",
                            "text": "必修/中国特色社会主义",
                            "remark": ""
                        },
                        {
                            "Id": "992503071614036650010287333506",
                            "text": "必修/经济与社会",
                            "remark": ""
                        },
                        {
                            "Id": "992503071614102250010310231558",
                            "text": "必修/政治与法治",
                            "remark": ""
                        },
                        {
                            "Id": "992503071614581480010373853188",
                            "text": "必修/哲学与文化",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615183500010410006045",
                            "text": "选择性必修/当代国际政治与经济",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615239060010445616923",
                            "text": "选择性必修/法律与生活",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615287830010462568267",
                            "text": "选择性必修/逻辑与思维",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615487860010506538985",
                            "text": "选修课/财经与生活",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615543390010536263460",
                            "text": "选修课/法官与律师",
                            "remark": ""
                        },
                        {
                            "Id": "992503071615595400010551680775",
                            "text": "选修课/历史上的哲学家",
                            "remark": ""
                        }
                    ],
                    "QuesPropArr": [
                        "必修/中国特色社会主义",
                        "必修/经济与社会",
                        "必修/政治与法治",
                        "必修/哲学与文化",
                        "选择性必修/当代国际政治与经济",
                        "选择性必修/法律与生活",
                        "选择性必修/逻辑与思维",
                        "选修课/财经与生活",
                        "选修课/法官与律师",
                        "选修课/历史上的哲学家"
                    ],
                    "SelectQuesPropText": "",
                    "NotifyType": "5",
                    "ValueObj": null,
                    "SelectQuesPropRemark": ""
                },
                {
                    "QuesPropName": "难度等级",
                    "QuesPropId": "992502191537002900008718163513",
                    "QuesPropSource": [
                        {
                            "Id": "992502191626430980014638761665",
                            "text": "难",
                            "remark": "试题内容复杂、考查要求高，主要考查考生的创新思维和解决复杂问题的能力。"
                        },
                        {
                            "Id": "992502191626477010014682660805",
                            "text": "中",
                            "remark": "试题内容较为复杂、考查要求较高，主要考查考生的综合思维能力和实践能力。"
                        },
                        {
                            "Id": "992502191626519030014732126300",
                            "text": "易",
                            "remark": "试题内容简单、考查要求较低，主要考查考生的基础知识和基本技能"
                        }
                    ],
                    "QuesPropArr": [
                        "难",
                        "中",
                        "易"
                    ],
                    "SelectQuesPropText": "",
                    "NotifyType": "2",
                    "ValueObj": null,
                    "SelectQuesPropRemark": ""
                },
                {
                    "QuesPropName": "考查要求",
                    "QuesPropId": "992502191537523980008886556169",
                    "QuesPropSource": [
                        {
                            "Id": "992502191709406670017727564216",
                            "text": "基础性",
                            "remark": "考查学生对主干知识和基本理论的掌握程度，关注今后生活、学习和工作所必须具备、不可或缺的知识、能力和素养。"
                        },
                        {
                            "Id": "992502191709463640017770356935",
                            "text": "综合性",
                            "remark": "体现在学科价值观与社会主义核心价值观的有机结合，考查学生对知识体系的系统性和整体性把握能力"
                        },
                        {
                            "Id": "992502191709507080017824636662",
                            "text": "应用性",
                            "remark": "体现在运用学科的知识和能力发现问题、分析问题、解决问题，运用正确的价值观和方法论，总结历史经验教训，为现实提供有意义、有价值的借鉴"
                        },
                        {
                            "Id": "992502191709551670017874982547",
                            "text": "创新性",
                            "remark": "体现在对科目资料进行新的解释和新的运用，对事物之间的联系进行新的发掘；对已有的观点、方法与结论进行批判性思考，得出新结论。"
                        }
                    ],
                    "QuesPropArr": [
                        "基础性",
                        "综合性",
                        "应用性",
                        "创新性"
                    ],
                    "SelectQuesPropText": "",
                    "NotifyType": "5",
                    "ValueObj": null,
                    "SelectQuesPropRemark": ""
                }
            ],
            "Childs": [
            ]
        }
    },
    "WorkflowPost": { // 模型信息   智能命题\试题克隆\智能生成 使用
        "NodeList": [
            {
                "NodeName": "试题命制",
                "NodeType": "AiSinglePrompt",
                "NodeContent": [
                    {
                        "NodePrompt": {
                            "功能": "按照给出的试题样例、试题类型、试题属性等信息及要求对试题进行命制",
                            "工作流": "1. 严格按照给出的试题结构等试题信息生成试题"
                        },
                        "NodeModel": {
                            "ModelName": "deepseek-r1",
                            "ModelArgs": {
                                "TopP": "0.5",
                                "TopK": "5",
                                "Temperature": "0.8",
                                "MaxToken": "2048",
                                "ApiKey": "sk-4z16Z80psmCcTTbPr7oxIXCTSZUAaoWwWtswRPifj1TXuVLA",
                                "ChatUrl": "https://api.lkeap.cloud.tencent.com/v1"
                            }
                        }
                    }
                ]
            }
        ]
    },
    "AgentPost": { // 任务角色信息   智能命题\试题克隆\智能生成 部分使用 ChatContent信息不是用，仅在智能生成使用
        "NodeContent": [
            {
                "NodePrompt": {
                    "总体任务": "根据给出的试题相关信息，如知识点、试题命制要求、试题结构、试题样例等信息对试题进行命制。",
                    "总体要求": "命制的试题质量要高，要素齐全，满足题型要求"
                },
                "NodeModel": null
            }
        ],
        "ChatContent": ""
    },
    "TaskName": "智能命题",
    "TaskId": "05f36ef4-c166-4311-b27d-68e814204908"
}```
/AgentGen 的返回参数：
1. 智能命题、智能克隆在入参AiQuesTypePost字段的数据上最外一层的QuesStr字段，将试题命制结果返回
2. 模型交互 返回的数据放在ChatContent

- **出参**：结构化题目数据（见文档示例）
- **异常处理**：返回标准错误码与错误信息

### 2. /GetTaskState
- **方法**：POST
- **功能**：查询任务进度/状态
- **入参**：{"TaskId": "..."}
- **出参**：{"TaskId": "...", "State": "...", "Msg": "..."}
- **异常处理**：任务不存在、已完成、失败等状态

### 3. /QueryStream
- **方法**：POST
- **功能**：获取任务中间verbose信息（如LLM推理流、工作流节点日志等）
- **入参**：{"TaskId": "..."}
- **出参**： SSE 方法： 返回f'data: {json.dumps(chunk, ensure_ascii=False)}\n\n'
- **异常处理**：任务不存在、无中间流等

需求分析

补充的信息：
1. 数据存储方案（数据库选择）： 使用缓存  需要请求结束后及时清除
2. 任务状态管理机制：创建一个全局状态管理状态的类，使用任务ID作为键，任务状态作为值，其中还有个满足/QueryStream返回的流式输出队列，这个部分请你设计一下。
3. 错误码定义   请你建议

## 任务状态管理机制设计

### 全局状态管理类设计

```python
from enum import Enum
from typing import Dict, Optional, List, Any
from dataclasses import dataclass
from queue import Queue
import asyncio
import time
import uuid

class TaskStatus(Enum):
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败

@dataclass
class TaskInfo:
    task_id: str
    status: TaskStatus
    created_at: float
    updated_at: float
    result: Optional[Dict] = None
    error_message: Optional[str] = None
    stream_queue: Optional[Queue] = None

class TaskStateManager:
    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
        self._lock = asyncio.Lock()
    
    async def create_task(self, task_id: str) -> TaskInfo:
        """创建新任务"""
        async with self._lock:
            task_info = TaskInfo(
                task_id=task_id,
                status=TaskStatus.PENDING,
                created_at=time.time(),
                updated_at=time.time(),
                stream_queue=Queue()
            )
            self._tasks[task_id] = task_info
            return task_info
    
    async def update_task_status(self, task_id: str, status: TaskStatus, 
                                result: Optional[Dict] = None, 
                                error_message: Optional[str] = None):
        """更新任务状态"""
        async with self._lock:
            if task_id in self._tasks:
                self._tasks[task_id].status = status
                self._tasks[task_id].updated_at = time.time()
                if result is not None:
                    self._tasks[task_id].result = result
                if error_message is not None:
                    self._tasks[task_id].error_message = error_message
    
    async def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        async with self._lock:
            return self._tasks.get(task_id)
    
    async def add_stream_data(self, task_id: str, data: Dict[str, Any]):
        """添加流式数据到队列"""
        async with self._lock:
            if task_id in self._tasks and self._tasks[task_id].stream_queue:
                self._tasks[task_id].stream_queue.put(data)
    
    async def get_stream_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取流式数据"""
        async with self._lock:
            if task_id in self._tasks and self._tasks[task_id].stream_queue:
                try:
                    return self._tasks[task_id].stream_queue.get_nowait()
                except:
                    return None
            return None
    
    async def cleanup_task(self, task_id: str):
        """清理任务数据（请求结束后调用）"""
        async with self._lock:
            if task_id in self._tasks:
                del self._tasks[task_id]

# 全局实例
task_manager = TaskStateManager()
```
### 2. /QueryStream
- **方法**：POST
- **功能**：根据请求发送的query_id sse 流式返回当前命题过程中的思考过程  即<think></think>标签内的内容
- **入参**：query_id
  - /QueryStream传入参数示例：
  - {
    "query_id":"eb81d3a5-e963-7d1e-8cce-eb9e32c18ebc"
}
- ##### 流式输出队列设计

流式队列输出设计 用来完成智能命题和试题克隆任务，使用异步队列进行数据处理。
将推理模型的思考过程，使用sse  流式返回给前端，我之前开发的这个接口请你参考
```python

@app.post("/QueryStream")
async def generate_response_with_id(request: QueryRequest):
    logger.info(f"输入的参数信息{request}")
    stream = get_global_variable()

    # 设置连接为活动状态
    stream_context.set_connection_active(request.query_id, True)

    async def generate(query_id):
        try:
            if query_id not in stream.keys():
                raise ValueError("query_id not found")

            stream_meta = stream[query_id]
            start_time = last_check_time = time.time()
            timeout_seconds = 60  # 设置超时时间为60秒
            connect_check_interval = 5
            while not stream_meta["is_completed"]:
                # 定期检查客户端连接状态
                current_time = time.time()
                if current_time - start_time > 300:
                    logger.warning(f"流式响应总时间超过5分钟，强制结束：{query_id}")
                    break

                # 定期检查客户端连接
                if current_time - last_check_time > connect_check_interval:  # 每5秒检查一次
                    if not stream_context.is_connection_active(query_id):
                        logger.info(f"客户端已断开连接: {query_id}")
                        break
                    last_check_time = current_time

                # 检查超时
                if current_time - last_check_time > timeout_seconds:
                    logger.warning(f"SSE连接超时: {query_id}")
                    break

                if stream_meta and "node_data_list" in stream_meta:
                    for node_name, var in stream_meta["node_data_list"].items():
                        try:
                            var.update_access_time()  # 更新访问时间

                            # 使用非阻塞方式获取数据
                            while not var.is_completed or not var.queue.empty():
                                try:
                                    data = var.get_nowait()

                                    if data == None:
                                        continue
                                    # print("queue data   ",data)
                                    if data is False:
                                        # 目前只传输第一个节点的思考过程的流
                                        yield "data: [DONE]\n\n"
                                        break

                                    current_time = datetime.now().strftime(
                                        '%Y-%m-%d %H:%M:%S.%f')[:-3]
                                    chunk = {
                                        "id": query_id,
                                        "node_name": node_name,
                                        "content": data,
                                        "current_time": current_time
                                    }

                                    yield f'data: {json.dumps(chunk, ensure_ascii=False)}\n\n'
                                except asyncio.QueueEmpty:
                                    break  # 队列为空,继续下一个节点
                                except Exception as e:
                                    logger.error(f"处理节点时数据出错：{node_name}, {e}")
                                    yield f'data: {json.dumps({"error": str(e)}, ensure_ascii=False)}\n\n'
                                    break

                                # 短暂暂停，避免CPU占用过高
                                await asyncio.sleep(0.01)
                        except Exception as e:
                            logger.error(f"处理节点时出错：{node_name}, {e}")
                            continue
                # 短暂暂停，避免CPU占用过高
                await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"{'=' * 10}流式响应生成过程中出错{'=' * 10}\n{e}")
            traceback.print_exc(file=open(LogFileName, "a"))
            error_response = {
                "error": {
                    "message": str(e),
                    "type": "server_error",
                    "code": "internal_error"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"
        finally:
            # 无论如何都要设置连接为非活动状态
            try:
                stream_context.set_connection_active(request.query_id, False)
                logger.info(f"连接已标记为非活动状态：{query_id}")
            except Exception as e:
                logger.error(f"设置连接状态时出错：{query_id}, {e}")

    return StreamingResponse(
        generate(request.query_id),
        media_type="text/event-stream; charset=utf-8",
        headers={
            "Cache-Control": "no-cache, no-transform",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream; charset=utf-8"
        }
    )
```
#### 流式结果实时返回
业务上  这个接口被调用的时间是在前端直接调用/AgentGen后，在业务设计上，想要把命题规划的思考过程，显示给前端，所以需要实现一个流式返回，要求命题规划步骤中模型交互的过程，一旦获得推理模型思考过程的响应，则立即返回给前端


### 3./GetTaskState
- **方法**：POST
- **功能**：查询任务进度/状态, 返回服务状态信息
- **入参**：query_id
  - /GetTaskState传入参数示例：
  - {
    "query_id":"eb81d3a5-e963-7d1e-8cce-eb9e32c18ebc"
}
- **出参**：{"Data": {"content": state_content, "is_completed": True|False }, "code": 200, "msg": "查询成功"}
- **异常处理**：任务不存在、已完成、失败等状态

##### 业务设计
该接口要求业务任务处理中 需要设定工作状态，并更新状态



## 错误码定义

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `404`: 任务不存在
- `500`: 服务器内部错误

## 业务设计

1. /AgentGen

试题克隆与智能命题的一个区别  即传入的参数中，智能命题没有试题内容，即原试题，试题克隆则有

参数映射：
```json
{"langMaterial": "【试题材料】",
                     "quesSubject":	"[试题题干]",
                     "choice":	'',
                     "answer":	'\n【答案】',
                     "resolev":	'\n【解析】',
                     'langAndSubject':	'【试题描述】',
                     "mulChoice": "多选试题选项"} 
                     ```


- 智能命题：根据传入的QuesPost中的试题结构，按照参数的映射，拼接成一个要求生成试题的命题输出结果示例，同时读取QuesCount的命题数量， quesprop的试题属性（知识点、难度、类型等）
  - 业务分类
    - 简单题型：（单选题、多选题、填空题、判断题、简答题）
      - 命题整体工作流逻辑：
        - 1. 按照试题命题要求（题型、难度、知识点、命题数量、命题素材）先生成命题规划，该命题规划中包含子任务列表；
        - 2. 根据子任务列表中的规划，进行子任务的生成；
        - 3. 将所有试题按照  f"{'-'*10}试题分割线{'-'*10}" 格式拼接，将其放在AiQuesTypePost放在最外层的QuesStr字段中，按照AiQuesTypePost 的 value 完成结构返回。
    - 复杂题型：（组合题）
      - 命题整体工作流逻辑：
      - 1. 获取最外层试题命题材料的结构，创建一个命题输出结果示例，获取子列表中的（知识点、命题素材），完成试题材料的命制工作；
      - 2. 将已经命好的试题材料添加到子任务的命题要求中充当命题素材，同时获取子列表中的（题型、难度、知识点、命题数量），进行命题规划，该命题规划中包含子任务列表（待完成的子题）；
      - 3. 将所有试（包含最先生成的试题材料）按照  f"{'-'*10}试题分割线{'-'*10}" 格式拼接，将其放在AiQuesTypePost放在最外层的QuesStr字段中，按照AiQuesTypePost 的 value 完成结构返回。

- 试题克隆：根据传入的QuesPost中的试题结构，按照参数的映射，拼接回原试题，充当克隆材料，同时读取QuesCount的命题数量， quesprop的试题属性（知识点、难度、类型等）
  - 业务分类
    - 简单题型：（单选题、多选题、填空题、判断题、简答题）
      - 命题整体工作流逻辑：
        - 1. 按照试题命题要求（题型、难度、知识点、命题数量、原试题）先生成命题规划，该命题规划中包含子任务列表；
        - 2. 根据子任务列表中的规划，进行子任务的生成；
        - 3. 将所有试题按照  f"{'-'*10}试题分割线{'-'*10}" 格式拼接，将其放在AiQuesTypePost放在最外层的QuesStr字段中，按照AiQuesTypePost 的 value 完成结构返回。
    - 复杂题型：（组合题）
      - 命题整体工作流逻辑：
      - 1. 获取最外层试题材料，克隆该试题材料；
      - 2. 将已经克隆好的试题材料添加到子任务的克隆要求中充当试题命题素材，同时获取子列表中的（题型、难度、知识点、命题数量、原实体），进行命题规划，该命题规划中包含子任务列表（待完成的子题）；
      - 3. 将所有试（包含最先生成的试题材料）按照  f"{'-'*10}试题分割线{'-'*10}" 格式拼接，将其放在AiQuesTypePost放在最外层的QuesStr字段中，按照AiQuesTypePost 的 value 完成结构返回。
- 模型交互：根据传入的chatContent, 将其放在配置的功能模板中，与模型交互，返回结果。





传入的参数ques_type_post的字段信息如下：
BaseName 存放的试题基础题型信息；
QuesTypeName存放的是试题业务题型信息；
ChoiceCount 存放的是试题选项数量，只有是选择题时，值不为0；
QuesCount 为需要命制的试题数量；
QuesProps 是一个数组， 里面存放的是试题的属性信息；
QuesPropName是试题的属性名称；
SelectQuesPropText 为属性值 ；
SelectQuesPropRemark 为试题属性值的补充信息，是命题的主要命题素材


