2025-08-14 14:28:07 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:07 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:742 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c8a29670-673c-9b42-b168-a8d30fc7a076', 'request_id': 'c8a29670-673c-9b42-b168-a8d30fc7a076'}
2025-08-14 14:28:08 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:08 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:742 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5d86a5f4-1ea0-9fc6-acba-5101780c0718', 'request_id': '5d86a5f4-1ea0-9fc6-acba-5101780c0718'}
2025-08-14 14:28:09 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:742 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 14:28:09 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1609 - 试题1生成最终失败，已重试3次
2025-08-14 14:28:09 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1564 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e', 'request_id': 'acf1f8c2-9ee7-9dd6-a8b5-81821c7c679e'}
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题1生成最终失败，已重试3次
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题2生成最终失败，已重试3次
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题3生成最终失败，已重试3次
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题1生成最终失败: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题2生成最终失败: 'count'
2025-08-14 15:39:02 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题3生成最终失败: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题1生成最终失败，已重试3次
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题2生成最终失败，已重试3次
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_single_subquestion_with_retry_async:890 - 子题3生成最终失败，已重试3次
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题1生成最终失败: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题2生成最终失败: 'count'
2025-08-14 15:44:17 | ERROR    | app.services.intelligent_question_service:_generate_subquestions_async:833 - 子题3生成最终失败: 'count'
