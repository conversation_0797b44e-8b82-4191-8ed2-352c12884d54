2025-08-11 18:08:23 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 0acc7929-34a2-7f74-c85e-cb269c874ee4
2025-08-11 18:08:23 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 18:09:34 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 0acc7929-34a2-7f74-c85e-cb269c874ee4
2025-08-11 18:09:34 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：0acc7929-34a2-7f74-c85e-cb269c874ee4
2025-08-11 18:10:31 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 18:14:04 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:14:04 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 18:15:03 | INFO     | app.api.stream:generate:157 - 连续空队列次数过多，结束连接: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:15:03 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:15:42 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-11 18:17:45 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: 解析结果验证失败
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:15 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:16 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:17 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:18 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:19 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:20 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:21 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:22 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:23 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:24 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:25 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:26 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:27 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:28 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:29 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:30 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:31 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:32 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:33 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:34 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:35 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:36 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:37 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:38 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:39 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:39 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:39 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:39 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:39 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:18:39 | WARNING  | app.core.state_manager:add_stream_data:143 - 队列满，移除旧数据后添加新数据: 1af5dc77-5902-ca8f-7542-64b3722cccdd
2025-08-11 18:19:34 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-11 18:21:09 | INFO     | app.core.state_manager:create_task:90 - 创建任务: baf9452d-9dfd-328e-ef2f-0fd999c76510
2025-08-11 18:21:09 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-11 18:22:10 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: baf9452d-9dfd-328e-ef2f-0fd999c76510
2025-08-11 18:22:10 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：baf9452d-9dfd-328e-ef2f-0fd999c76510
2025-08-11 18:22:59 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 11:43:18 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 9592f217-6043-5e8a-e7db-503daa3431a0
2025-08-12 11:43:18 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 11:43:18 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 11:43:18 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:18 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:18 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:18 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'bfc3f0be-0c0c-9fe1-8c2d-e93262e1ef69'}
2025-08-12 11:43:18 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 9592f217-6043-5e8a-e7db-503daa3431a0
2025-08-12 11:43:18 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：9592f217-6043-5e8a-e7db-503daa3431a0
2025-08-12 11:43:19 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:19 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:19 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:19 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '8647ed8f-6533-9100-bc37-3d1b11c0293f'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'b9cb20ce-6450-904e-aa92-3a70580d57bc'}
2025-08-12 11:43:20 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '575eff3f-2ed6-9dba-a1a9-05d99ac78c45'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:20 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'de8bfcb3-3c54-9e5b-89f6-435a8e3df002'}
2025-08-12 11:43:21 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '38bb416d-357b-9586-87b9-19d97e2dc594'}
2025-08-12 11:43:21 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:21 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': 'dd8ea35f-3c78-96ec-a403-bbf3eda97de9'}
2025-08-12 11:43:22 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '4c89042c-94e5-9818-a157-f9ef9cddd475'}
2025-08-12 11:43:22 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 11:43:22 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'error': {'message': 'The model `deepseek-chat` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}, 'request_id': '89418e39-309f-9675-bfd9-282e3879604f'}
2025-08-12 11:43:22 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 11:45:15 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 11:45:15 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 11:46:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: e44a35f7-dcd6-3842-31bc-170f368420b1
2025-08-12 11:46:30 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 11:46:40 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 11:50:27 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 11:50:27 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 11:50:35 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 2e7690e8-9ebe-8832-8d59-e4e5dfeedd63
2025-08-12 11:50:35 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 11:50:41 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 11:51:46 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 2e7690e8-9ebe-8832-8d59-e4e5dfeedd63
2025-08-12 11:51:46 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：2e7690e8-9ebe-8832-8d59-e4e5dfeedd63
2025-08-12 11:52:25 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f0768fa3-8ee0-9e1d-b063-28191206f776', 'request_id': 'f0768fa3-8ee0-9e1d-b063-28191206f776'}
2025-08-12 11:52:25 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:25 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-933601f4-7c9d-937f-80d9-c81db5a4850f', 'request_id': '933601f4-7c9d-937f-80d9-c81db5a4850f'}
2025-08-12 11:52:26 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-869186b2-94de-983f-9b4e-4ea37995fdcd', 'request_id': '869186b2-94de-983f-9b4e-4ea37995fdcd'}
2025-08-12 11:52:26 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:26 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7c6d2c22-6295-9358-ba7d-9834f5f8b18c', 'request_id': '7c6d2c22-6295-9358-ba7d-9834f5f8b18c'}
2025-08-12 11:52:27 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-56e7a057-8c78-929a-9b7b-e5de5912cde3', 'request_id': '56e7a057-8c78-929a-9b7b-e5de5912cde3'}
2025-08-12 11:52:27 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 11:52:27 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0ae2a0f5-3f03-9a52-acdf-13e784425752', 'request_id': '0ae2a0f5-3f03-9a52-acdf-13e784425752'}
2025-08-12 11:52:27 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 12:03:09 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 12:03:09 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 13:35:16 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 93eb0c99-ea7c-eebf-3f92-252b20f4a076
2025-08-12 13:35:16 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 13:35:16 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 13:35:37 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 93eb0c99-ea7c-eebf-3f92-252b20f4a076
2025-08-12 13:35:37 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：93eb0c99-ea7c-eebf-3f92-252b20f4a076
2025-08-12 13:35:37 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-7b23da22-63b7-9305-be64-090bc2573aaa', 'request_id': '7b23da22-63b7-9305-be64-090bc2573aaa'}
2025-08-12 13:35:38 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-ca52134b-c99b-9a41-9466-88093248a824', 'request_id': 'ca52134b-c99b-9a41-9466-88093248a824'}
2025-08-12 13:35:38 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-b8865e42-6120-9fc9-b3a4-b9c675a3f49d', 'request_id': 'b8865e42-6120-9fc9-b3a4-b9c675a3f49d'}
2025-08-12 13:35:39 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:39 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-0b58921e-6afb-9d34-a5d1-9d9a88dece66', 'request_id': '0b58921e-6afb-9d34-a5d1-9d9a88dece66'}
2025-08-12 13:35:40 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-63ab1655-e468-9ebe-af3e-fa9af7107ee5', 'request_id': '63ab1655-e468-9ebe-af3e-fa9af7107ee5'}
2025-08-12 13:35:40 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 13:35:40 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-55e8ddff-9e1c-905b-b834-86901d4b23e2', 'request_id': '55e8ddff-9e1c-905b-b834-86901d4b23e2'}
2025-08-12 13:35:40 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 13:40:30 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 13:40:30 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 13:41:20 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 473b0e83-ea7e-e7e3-c3be-fd83279249b6
2025-08-12 13:41:20 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 13:41:20 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 13:41:44 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 473b0e83-ea7e-e7e3-c3be-fd83279249b6
2025-08-12 13:41:44 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：473b0e83-ea7e-e7e3-c3be-fd83279249b6
2025-08-12 13:41:44 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-cd4a9e58-7f14-97d9-8846-e1d131d21042', 'request_id': 'cd4a9e58-7f14-97d9-8846-e1d131d21042'}
2025-08-12 13:41:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:44 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f7144a51-32a3-99e4-9bf9-eed08f621911', 'request_id': 'f7144a51-32a3-99e4-9bf9-eed08f621911'}
2025-08-12 13:41:45 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-948ea772-ca7d-9785-806b-fb4b90cfd901', 'request_id': '948ea772-ca7d-9785-806b-fb4b90cfd901'}
2025-08-12 13:41:45 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:45 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11', 'request_id': '4cbf9aa4-c8db-97af-ad89-70a3d7c5fd11'}
2025-08-12 13:41:46 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-8c37d8cc-00d7-97ba-8f67-bf5ec4a57db3', 'request_id': '8c37d8cc-00d7-97ba-8f67-bf5ec4a57db3'}
2025-08-12 13:41:47 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 13:41:47 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-52e73c33-52b4-9d22-a4c3-0602ccb5fb59', 'request_id': '52e73c33-52b4-9d22-a4c3-0602ccb5fb59'}
2025-08-12 13:41:47 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 13:44:52 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 0d9d3ed2-b8ca-bfd3-f526-aa738a83ec47
2025-08-12 13:44:52 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 13:45:10 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 0d9d3ed2-b8ca-bfd3-f526-aa738a83ec47
2025-08-12 13:45:10 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：0d9d3ed2-b8ca-bfd3-f526-aa738a83ec47
2025-08-12 13:53:45 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 13:53:45 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 13:53:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 87899300-8112-00c6-55dd-4d54022267c3
2025-08-12 13:53:50 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 13:53:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 13:56:02 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：87899300-8112-00c6-55dd-4d54022267c3
2025-08-12 13:56:27 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-12 13:57:13 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 13:57:13 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 13:57:32 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 18294592-bd12-8a53-1070-abe0a1808d62
2025-08-12 13:57:32 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 13:57:32 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 13:57:46 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:57:46 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:57:46 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:57:46 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:57:46 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 18294592-bd12-8a53-1070-abe0a1808d62
2025-08-12 13:57:46 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：18294592-bd12-8a53-1070-abe0a1808d62
2025-08-12 13:58:37 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:37 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:37 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:37 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:39 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-12 13:58:39 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:41 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:44 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-12 13:58:48 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 13:58:48 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 14:08:03 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cf9f9154-3089-090f-8fd3-3cf9ac385456
2025-08-12 14:08:03 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: cf9f9154-3089-090f-8fd3-3cf9ac385456
2025-08-12 14:08:35 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：cf9f9154-3089-090f-8fd3-3cf9ac385456
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:35 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:36 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-12 14:08:36 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: unsupported operand type(s) for ** or pow(): 'bool' and 'dict'
2025-08-12 14:08:58 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:08:58 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:09:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ba5a0136-49c5-7e3c-2380-132b18082f2a
2025-08-12 14:09:06 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:09:06 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:09:28 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: ba5a0136-49c5-7e3c-2380-132b18082f2a
2025-08-12 14:09:28 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：ba5a0136-49c5-7e3c-2380-132b18082f2a
2025-08-12 14:10:40 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-c2314af5-9f35-92aa-a6be-06246d8866f2', 'request_id': 'c2314af5-9f35-92aa-a6be-06246d8866f2'}
2025-08-12 14:16:17 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:16:17 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:17:26 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 7996be87-0dc9-101f-5d63-a6d496891fdd
2025-08-12 14:17:26 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:17:26 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:17:47 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 7996be87-0dc9-101f-5d63-a6d496891fdd
2025-08-12 14:17:47 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：7996be87-0dc9-101f-5d63-a6d496891fdd
2025-08-12 14:19:44 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:19:44 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:20:06 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 5920b890-fb2a-a02f-e43c-cd899bb4bd8c
2025-08-12 14:20:06 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:20:06 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:20:32 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 5920b890-fb2a-a02f-e43c-cd899bb4bd8c
2025-08-12 14:20:32 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：5920b890-fb2a-a02f-e43c-cd899bb4bd8c
2025-08-12 14:21:25 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-15df6dd4-688a-978a-b8ac-58f4623e815d', 'request_id': '15df6dd4-688a-978a-b8ac-58f4623e815d'}
2025-08-12 14:21:25 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:21:25 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-379b3674-64de-96fd-aa76-743adb403622', 'request_id': '379b3674-64de-96fd-aa76-743adb403622'}
2025-08-12 14:25:13 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:25:13 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:25:32 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 861c81af-6072-ec0f-b45d-6ad95a844f33
2025-08-12 14:25:32 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:25:32 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:25:54 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 861c81af-6072-ec0f-b45d-6ad95a844f33
2025-08-12 14:25:54 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：861c81af-6072-ec0f-b45d-6ad95a844f33
2025-08-12 14:25:54 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-12 14:28:18 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:28:18 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:28:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: cf9ddd46-dd04-c412-e039-8c591c9e6b39
2025-08-12 14:28:30 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:28:30 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:29:14 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: cf9ddd46-dd04-c412-e039-8c591c9e6b39
2025-08-12 14:29:14 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：cf9ddd46-dd04-c412-e039-8c591c9e6b39
2025-08-12 14:29:14 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-12 14:30:08 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-f0ed45dc-6d22-9fc6-8ae4-831fe738610a', 'request_id': 'f0ed45dc-6d22-9fc6-8ae4-831fe738610a'}
2025-08-12 14:30:08 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:30:08 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-28388457-33cc-9cca-bafe-b03b8e09d971', 'request_id': '28388457-33cc-9cca-bafe-b03b8e09d971'}
2025-08-12 14:31:02 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:31:02 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:31:07 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 1bc7ce0f-97f4-808d-dfa4-733b2f8e279e
2025-08-12 14:31:07 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:31:07 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:31:31 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 1bc7ce0f-97f4-808d-dfa4-733b2f8e279e
2025-08-12 14:31:31 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：1bc7ce0f-97f4-808d-dfa4-733b2f8e279e
2025-08-12 14:31:59 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-b4cc4ca7-888d-9fbb-be4f-b3fba00abfd1', 'request_id': 'b4cc4ca7-888d-9fbb-be4f-b3fba00abfd1'}
2025-08-12 14:31:59 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:31:59 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': 'parameter.enable_thinking must be set to false for non-streaming calls', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-5677abeb-c733-97ac-a17a-ad49b080df87', 'request_id': '5677abeb-c733-97ac-a17a-ad49b080df87'}
2025-08-12 14:35:27 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 14:35:27 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 14:35:34 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 369296c5-7cec-a28c-8ba2-9e7e26a2fb3b
2025-08-12 14:35:34 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-12 14:35:34 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 14:36:12 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 369296c5-7cec-a28c-8ba2-9e7e26a2fb3b
2025-08-12 14:36:12 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：369296c5-7cec-a28c-8ba2-9e7e26a2fb3b
2025-08-12 14:36:51 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-12 15:05:18 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 15:05:18 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 15:06:02 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f1bae16a-aeae-5685-df9f-b8edd5452ac4
2025-08-12 15:06:02 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:06:02 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-12 15:06:30 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f1bae16a-aeae-5685-df9f-b8edd5452ac4
2025-08-12 15:06:30 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f1bae16a-aeae-5685-df9f-b8edd5452ac4
2025-08-12 15:07:16 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:08:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: faf23cb7-4ec4-dac0-8355-c4e00e03f18c
2025-08-12 15:08:43 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:08:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-12 15:08:43 | ERROR    | app.services.llm_manager:_call_generic_stream_api:547 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:43 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:43 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:544 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:43 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1268 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: faf23cb7-4ec4-dac0-8355-c4e00e03f18c
2025-08-12 15:08:44 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：faf23cb7-4ec4-dac0-8355-c4e00e03f18c
2025-08-12 15:08:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:547 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:544 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:44 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1268 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:_call_generic_stream_api:547 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:544 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1268 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1285 - 命题规划最终失败，已重试 3 次
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1289 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:770 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:45 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1664 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:770 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1664 - 试题1生成失败（尝试2/3）: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | WARNING  | app.services.llm_manager:_call_langchain_api:238 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:243 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:_call_langchain_api:249 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:770 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1664 - 试题1生成失败（尝试3/3）: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1673 - 试题1生成最终失败，已重试3次
2025-08-12 15:08:46 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1628 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `default` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-12 15:08:46 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:09:37 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f67af9f3-0473-026f-e300-c3af0fae1267
2025-08-12 15:09:37 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:09:58 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f67af9f3-0473-026f-e300-c3af0fae1267
2025-08-12 15:09:58 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f67af9f3-0473-026f-e300-c3af0fae1267
2025-08-12 15:10:30 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:41:30 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d8a23df6-f9fa-d004-6b89-6a167688ad1e
2025-08-12 15:41:30 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:42:00 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: d8a23df6-f9fa-d004-6b89-6a167688ad1e
2025-08-12 15:42:00 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：d8a23df6-f9fa-d004-6b89-6a167688ad1e
2025-08-12 15:42:26 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:42:45 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8de9d109-8edf-0d94-e72d-62841c9463b7
2025-08-12 15:42:45 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:43:21 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8de9d109-8edf-0d94-e72d-62841c9463b7
2025-08-12 15:43:21 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8de9d109-8edf-0d94-e72d-62841c9463b7
2025-08-12 15:43:55 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:44:53 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f2eb17fc-f96c-f758-d8ba-aa94c4aaf5af
2025-08-12 15:44:53 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:45:25 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f2eb17fc-f96c-f758-d8ba-aa94c4aaf5af
2025-08-12 15:45:25 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f2eb17fc-f96c-f758-d8ba-aa94c4aaf5af
2025-08-12 15:46:15 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 810c63a3-3118-0ca1-ed6f-aa91f4f76b9b
2025-08-12 15:46:15 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:46:51 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:46:51 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 810c63a3-3118-0ca1-ed6f-aa91f4f76b9b
2025-08-12 15:46:51 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：810c63a3-3118-0ca1-ed6f-aa91f4f76b9b
2025-08-12 15:47:32 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 15:48:38 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 052ca7da-c6fa-d010-f93f-93873aac0b93
2025-08-12 15:48:38 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 15:49:04 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 052ca7da-c6fa-d010-f93f-93873aac0b93
2025-08-12 15:49:04 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：052ca7da-c6fa-d010-f93f-93873aac0b93
2025-08-12 15:49:39 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 16:16:10 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 16:16:10 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 16:16:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 95f7b324-2681-14b6-39d2-dbe8b8989649
2025-08-12 16:16:31 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 16:16:31 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 16:17:02 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 95f7b324-2681-14b6-39d2-dbe8b8989649
2025-08-12 16:17:02 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：95f7b324-2681-14b6-39d2-dbe8b8989649
2025-08-12 16:17:02 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1600 - 试题1生成失败（尝试1/3）: 'ConfigManager' object is not subscriptable
2025-08-12 16:17:03 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1600 - 试题1生成失败（尝试2/3）: 'ConfigManager' object is not subscriptable
2025-08-12 16:17:04 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1600 - 试题1生成失败（尝试3/3）: 'ConfigManager' object is not subscriptable
2025-08-12 16:17:04 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1609 - 试题1生成最终失败，已重试3次
2025-08-12 16:17:04 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1564 - 试题1生成最终失败: 'ConfigManager' object is not subscriptable
2025-08-12 16:17:04 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-12 16:19:39 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-12 16:19:39 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-12 16:19:43 | INFO     | app.core.state_manager:create_task:90 - 创建任务: ae50269f-e7f5-d896-91c6-9d9d18e18199
2025-08-12 16:19:43 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-12 16:19:43 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-12 16:20:37 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: ae50269f-e7f5-d896-91c6-9d9d18e18199
2025-08-12 16:20:37 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：ae50269f-e7f5-d896-91c6-9d9d18e18199
2025-08-12 16:20:37 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1204 - 命题规划尝试 1 失败: 解析结果验证失败
2025-08-12 16:21:11 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-13 08:53:03 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-13 08:53:03 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-13 13:33:59 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 8db9f701-3c22-ed39-3b5a-c8f7aa7527d5
2025-08-13 13:33:59 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-13 13:33:59 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-13 13:34:20 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 8db9f701-3c22-ed39-3b5a-c8f7aa7527d5
2025-08-13 13:34:20 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：8db9f701-3c22-ed39-3b5a-c8f7aa7527d5
2025-08-13 13:34:39 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-13 13:35:12 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 0a666747-c15e-38f4-c44a-9e6a1f7e2d04
2025-08-13 13:35:12 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-13 13:35:46 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 0a666747-c15e-38f4-c44a-9e6a1f7e2d04
2025-08-13 13:35:46 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：0a666747-c15e-38f4-c44a-9e6a1f7e2d04
2025-08-13 13:36:05 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
2025-08-13 13:36:55 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-13 13:36:55 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-13 13:37:10 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 27bca803-2cce-3397-99f3-f18c5b363294
2025-08-13 13:37:10 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-13 13:37:10 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-13 13:37:28 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 27bca803-2cce-3397-99f3-f18c5b363294
2025-08-13 13:37:28 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：27bca803-2cce-3397-99f3-f18c5b363294
2025-08-13 13:38:41 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-13 13:38:41 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-13 13:39:33 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6db78afa-d93c-1b7f-fe9a-f0afbbd6327f
2025-08-13 13:39:33 | INFO     | app.services.intelligent_question_service:generate_questions:40 - 开始智能命题
2025-08-13 13:39:33 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: qwen3-235b-a22b
2025-08-13 13:40:09 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 6db78afa-d93c-1b7f-fe9a-f0afbbd6327f
2025-08-13 13:40:09 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：6db78afa-d93c-1b7f-fe9a-f0afbbd6327f
2025-08-13 13:40:25 | INFO     | app.services.intelligent_question_service:generate_questions:93 - 智能命题完成
