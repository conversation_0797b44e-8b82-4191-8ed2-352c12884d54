from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
from app.models.schemas import StreamRequest
from app.core.state_manager import task_manager, TaskStatus
import json
import asyncio
import time
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/QueryStream")
async def query_stream(request: Request):
    """获取任务中间流式信息（SSE）"""
    try:
        body = await request.json()
        task_id = body.get("TaskId") or body.get("query_id")

        if not task_id:
            return StreamingResponse(
                (b'data: {"error": "TaskId cannot be empty"}\n\n',),
                media_type="text/event-stream"
            )

        # 设置连接为活动状态
        await task_manager.stream_context.set_connection_active(task_id, True)

        async def generate(task_id):
            try:
                # 检查任务是否存在
                task_info = await task_manager.get_task_info(task_id)
                if not task_info:
                    yield f'data: {json.dumps({"error": "TaskId not found"}, ensure_ascii=False)}\n\n'
                    return

                start_time = last_check_time = time.time()
                timeout_seconds = 60  # 设置超时时间为60秒
                connect_check_interval = 5  # 每5秒检查一次连接状态
                empty_queue_count = 0  # 空队列计数
                max_empty_count = 100  # 最大空队列次数（10秒）

                while True:
                    # 定期检查客户端连接状态
                    current_time = time.time()
                    if current_time - start_time > 300:  # 5分钟总超时
                        logger.warning(f"流式响应总时间超过5分钟，强制结束：{task_id}")
                        break

                    # 定期检查客户端连接
                    if current_time - last_check_time > connect_check_interval:
                        if not await task_manager.stream_context.is_connection_active(task_id):
                            logger.info(f"客户端已断开连接: {task_id}")
                            break
                        last_check_time = current_time

                    # 检查超时
                    if current_time - last_check_time > timeout_seconds:
                        logger.warning(f"SSE连接超时: {task_id}")
                        break

                    # 重新获取任务信息（状态可能已更新）
                    task_info = await task_manager.get_task_info(task_id)
                    if not task_info:
                        logger.warning(f"任务不存在: {task_id}")
                        break

                    # 获取流式数据
                    data_found = False
                    if task_info.stream_queue:
                        logger.debug(
                            f"检查任务 {task_id} 的流式队列，队列大小: {task_info.stream_queue.qsize()}")
                        try:
                            # 使用非阻塞方式获取数据
                            while not task_info.stream_queue.empty():
                                try:
                                    data = task_info.stream_queue.get_nowait()
                                    logger.debug(f"从队列获取到数据: {data}")

                                    if data is None:
                                        logger.debug("数据为None，跳过")
                                        continue

                                    if data is False:
                                        # 流式输出完成标记
                                        logger.info(
                                            f"收到完成标记，结束流式输出: {task_id}")
                                        yield "data: [DONE]\n\n"
                                        return

                                    current_time_str = datetime.now().strftime(
                                        '%Y-%m-%d %H:%M:%S.%f')[:-3]

                                    # 提取实际的模型输出内容
                                    content = data.get("message", "")

                                    # 如果是思考过程数据，优先提取thinking字段
                                    if data.get("type") == "THINKING" and data.get("data", {}).get("thinking"):
                                        content = data.get("data", {}).get(
                                            "thinking", content)
                                    # 如果是状态更新数据，使用message字段
                                    elif data.get("type") == "STATUS_UPDATE":
                                        content = data.get("message", content)
                                    # 如果是其他类型的流式数据，直接使用message
                                    elif data.get("type") == "thinking_complete":
                                        content = data.get("content", content)

                                    chunk = {
                                        "id": task_id,
                                        "node_name": data.get("data", {}).get("step", "thinking_process"),
                                        "content": content,
                                        "current_time": current_time_str
                                    }

                                    logger.debug(f"准备发送数据块: {chunk}")
                                    yield f'data: {json.dumps(chunk, ensure_ascii=False)}\n\n'
                                    data_found = True
                                    empty_queue_count = 0  # 重置空队列计数

                                except asyncio.QueueEmpty:
                                    logger.debug("队列为空，跳出内层循环")
                                    break  # 队列为空，跳出内层循环
                                except Exception as e:
                                    logger.error(f"处理流式数据时出错：{e}")
                                    yield f'data: {json.dumps({"error": str(e)}, ensure_ascii=False)}\n\n'
                                    break
                        except Exception as e:
                            logger.error(f"处理流式队列时出错：{e}")
                            continue
                    else:
                        logger.warning(f"任务 {task_id} 没有流式队列")

                    # 检查任务是否完成（在获取数据后检查）
                    if task_info.is_completed:
                        logger.info(f"任务完成，结束流式输出: {task_id}")
                        # 确保发送完成标记
                        yield "data: [DONE]\n\n"
                        break

                    # 如果没有找到数据，增加空队列计数
                    if not data_found:
                        empty_queue_count += 1

                        # 如果连续空队列次数过多，发送心跳保持连接
                        if empty_queue_count % 20 == 0:  # 每2秒发送一次心跳
                            heartbeat = {
                                "id": task_id,
                                "node_name": "heartbeat",
                                "content": "连接保持中...",
                                "current_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                            }
                            yield f'data: {json.dumps(heartbeat, ensure_ascii=False)}\n\n'

                    #     如果空队列次数过多，结束连接（避免无限等待）
                        if empty_queue_count > max_empty_count:
                            logger.info(f"连续空队列次数过多，结束连接: {task_id}")
                            # 发送完成标记
                            yield "data: [DONE]\n\n"
                            break

                    # 短暂暂停，避免CPU占用过高
                    await asyncio.sleep(0.05)

            except Exception as e:
                logger.error(f"{'=' * 10}流式响应生成过程中出错{'=' * 10}\n{e}")
                error_response = {
                    "error": {
                        "message": str(e),
                        "type": "server_error",
                        "code": "internal_error"
                    }
                }
                yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
            finally:
                # 无论如何都要设置连接为非活动状态
                try:
                    await task_manager.stream_context.set_connection_active(task_id, False)
                    logger.info(f"连接已标记为非活动状态：{task_id}")
                except Exception as e:
                    logger.error(f"设置连接状态时出错：{task_id}, {e}")

        return StreamingResponse(
            generate(task_id),
            media_type="text/event-stream; charset=utf-8",
            headers={
                "Cache-Control": "no-cache, no-transform",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream; charset=utf-8"
            }
        )

    except Exception as e:
        logger.error(f"QueryStream接口错误: {e}")
        return StreamingResponse(
            (f'data: {json.dumps({"error": str(e)}, ensure_ascii=False)}\n\n'.encode(),),
            media_type="text/event-stream"
        )
