2025-08-06 18:07:21 | INFO     | app.core.state_manager:create_task:90 - 创建任务: add024cb-13ae-9f19-8fcb-23696cb5f438
2025-08-06 18:07:21 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:07:21 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: GLM-4.5-Air
2025-08-06 18:07:21 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:21 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:21 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:21 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: add024cb-13ae-9f19-8fcb-23696cb5f438
2025-08-06 18:07:22 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：add024cb-13ae-9f19-8fcb-23696cb5f438
2025-08-06 18:07:22 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:22 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:23 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:07:24 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:07:24 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-06 18:09:42 | INFO     | app.core.state_manager:create_task:90 - 创建任务: f6a26e83-efb2-d14b-eb73-f77d223fdd49
2025-08-06 18:09:42 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:09:42 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:42 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: f6a26e83-efb2-d14b-eb73-f77d223fdd49
2025-08-06 18:09:42 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：f6a26e83-efb2-d14b-eb73-f77d223fdd49
2025-08-06 18:09:43 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:43 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:43 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:43 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:09:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 91049012-8da4-8aa5-9117-0644676add81
2025-08-06 18:18:00 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:18:00 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: GLM-4.5-Air
2025-08-06 18:18:00 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:00 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 91049012-8da4-8aa5-9117-0644676add81
2025-08-06 18:18:00 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：91049012-8da4-8aa5-9117-0644676add81
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:01 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:02 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:18:03 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:18:03 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-06 18:21:17 | INFO     | app.core.state_manager:create_task:90 - 创建任务: caf06c02-11f6-42d5-4bad-a44e33481f70
2025-08-06 18:21:17 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: caf06c02-11f6-42d5-4bad-a44e33481f70
2025-08-06 18:21:17 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：caf06c02-11f6-42d5-4bad-a44e33481f70
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:17 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:18 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:19 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:21:20 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:21:20 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-06 18:22:29 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-06 18:22:29 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-06 18:23:31 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 342f9097-c4f5-3815-3ed2-08caf4392d20
2025-08-06 18:23:31 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:23:31 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: GLM-4.5-Air
2025-08-06 18:23:31 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:31 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 342f9097-c4f5-3815-3ed2-08caf4392d20
2025-08-06 18:23:31 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：342f9097-c4f5-3815-3ed2-08caf4392d20
2025-08-06 18:23:32 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:32 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:32 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:32 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:33 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:23:34 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:23:34 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-06 18:26:19 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-06 18:26:19 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-06 18:26:19 | INFO     | __main__:lifespan:70 - 智能命题系统关闭中...
2025-08-06 18:26:42 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-06 18:26:42 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-06 18:27:15 | INFO     | app.core.state_manager:create_task:90 - 创建任务: afaa3253-17b5-0117-956e-4002d7c6108d
2025-08-06 18:27:15 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:27:15 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: GLM-4.5-Air
2025-08-06 18:28:20 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:28:20 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: afaa3253-17b5-0117-956e-4002d7c6108d
2025-08-06 18:28:20 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：afaa3253-17b5-0117-956e-4002d7c6108d
2025-08-06 18:30:53 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-08-06 18:30:53 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-08-06 18:31:26 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 491d7c2e-d52a-82f3-3e7a-a30e456b26fe
2025-08-06 18:31:26 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:31:26 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: GLM-4.5-Air
2025-08-06 18:31:26 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:26 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 491d7c2e-d52a-82f3-3e7a-a30e456b26fe
2025-08-06 18:31:26 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：491d7c2e-d52a-82f3-3e7a-a30e456b26fe
2025-08-06 18:31:27 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:27 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:27 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:27 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:28 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:31:29 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Error code: 404 - {'object': 'error', 'message': 'The model `deepseek-chat` does not exist.', 'type': 'NotFoundError', 'param': None, 'code': 404}
2025-08-06 18:31:29 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-06 18:34:36 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 94d5e062-10d0-f510-2fbc-81cdbd958dfd
2025-08-06 18:34:36 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:34:36 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-06 18:34:37 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Connection error.
2025-08-06 18:34:37 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-06 18:34:37 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Connection error.
2025-08-06 18:34:37 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 1 失败: Connection error.
2025-08-06 18:34:37 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: 94d5e062-10d0-f510-2fbc-81cdbd958dfd
2025-08-06 18:34:37 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：94d5e062-10d0-f510-2fbc-81cdbd958dfd
2025-08-06 18:34:39 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Connection error.
2025-08-06 18:34:39 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-06 18:34:39 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Connection error.
2025-08-06 18:34:39 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 2 失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.llm_manager:_call_generic_stream_api:546 - 通用流式API调用失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.llm_manager:generate_text_with_stream:169 - LLM流式调用失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.intelligent_question_service:_call_llm_with_stream:543 - LLM流式调用失败: Connection error.
2025-08-06 18:34:41 | WARNING  | app.services.intelligent_question_service:_generate_planning_with_retry:1267 - 命题规划尝试 3 失败: Connection error.
2025-08-06 18:34:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1284 - 命题规划最终失败，已重试 3 次
2025-08-06 18:34:41 | ERROR    | app.services.intelligent_question_service:_generate_planning_with_retry:1288 - 命题规划重试失败，使用默认规划: Connection error.
2025-08-06 18:34:43 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-06 18:34:44 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Connection error.
2025-08-06 18:34:44 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试1/3）: Connection error.
2025-08-06 18:34:46 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-06 18:34:47 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Connection error.
2025-08-06 18:34:47 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试2/3）: Connection error.
2025-08-06 18:34:49 | WARNING  | app.services.llm_manager:_call_langchain_api:237 - ainvoke 调用失败，降级使用 invoke: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.llm_manager:_call_langchain_api:242 - invoke 降级调用也失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.llm_manager:_call_langchain_api:248 - LangChain API调用失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.llm_manager:generate_text:103 - LLM调用失败: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.intelligent_question_service:_call_llm_non_stream:769 - LLM非流式调用失败: Connection error.
2025-08-06 18:34:50 | WARNING  | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1663 - 试题1生成失败（尝试3/3）: Connection error.
2025-08-06 18:34:50 | ERROR    | app.services.intelligent_question_service:_generate_single_question_with_retry_async:1672 - 试题1生成最终失败，已重试3次
2025-08-06 18:34:50 | ERROR    | app.services.intelligent_question_service:_generate_questions_async:1627 - 试题1生成最终失败: Connection error.
2025-08-06 18:34:50 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-08-06 18:36:16 | INFO     | app.core.state_manager:create_task:90 - 创建任务: d63614cf-01f3-329a-6329-ac6d9a713f94
2025-08-06 18:36:16 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-08-06 18:36:16 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-08-06 18:36:54 | INFO     | app.api.stream:generate:87 - 收到完成标记，结束流式输出: d63614cf-01f3-329a-6329-ac6d9a713f94
2025-08-06 18:36:54 | INFO     | app.api.stream:generate:180 - 连接已标记为非活动状态：d63614cf-01f3-329a-6329-ac6d9a713f94
2025-08-06 18:37:33 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
