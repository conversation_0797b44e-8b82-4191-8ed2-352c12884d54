# 智能审核功能技术方案

## 1. 项目背景与现状

### 1.1 现有技术架构
当前智能命题系统基于现代化的技术栈构建，具备良好的扩展性基础：

- **核心框架**: FastAPI + LangChain + LangGraph
- **模型生态**: 支持多种主流LLM模型（DeepSeek、GPT-4、Qwen等）
- **任务引擎**: 异步任务管理，支持流式输出和状态追踪
- **配置系统**: 基于YAML的灵活配置管理
- **API设计**: RESTful架构，已实现智能命题、试题克隆等核心功能

### 1.2 现有模块结构
```
app/
├── api/          # API路由层 - 对外接口定义
├── services/     # 业务服务层 - 核心业务逻辑
├── core/         # 核心功能层 - 状态管理、任务调度
├── models/       # 数据模型层 - 数据结构定义
└── utils/        # 工具类层 - 通用工具函数
```

## 2. 智能审核功能需求分析

### 2.1 核心业务需求

#### 2.1.1 试题内容审核
- **完整性检测**: 验证试题结构完整性（题目、选项、答案、解析）
- **逻辑一致性**: 检查题目与答案的逻辑关系
- **内容准确性**: 验证参考答案和解析的正确性

#### 2.1.2 表达规范检测
- **语言规范**: 错别字、语法错误、表达不当检测
- **格式规范**: 标点符号、数学公式、图表格式检测
- **术语规范**: 专业术语使用的准确性和一致性

#### 2.1.3 属性标注验证
- **知识点匹配**: 验证试题内容与标注知识点的匹配度
- **难度评估**: 基于内容复杂度评估难度层次的合理性
- **考核目标**: 验证试题是否达到预期的考核目标

#### 2.1.4 合规性检测
- **敏感内容**: 政治敏感、暴力、歧视等不当内容检测
- **教育适宜性**: 符合教育价值观和年龄适宜性
- **版权合规**: 避免侵权内容和不当引用

### 2.2 技术实现需求

#### 2.2.1 架构需求
- **多Agent协作**: 基于LangGraph构建专业化Agent系统
- **异步处理**: 支持高并发审核任务和实时状态更新
- **模块化设计**: 便于功能扩展和维护升级

#### 2.2.2 性能需求
- **响应时间**: 单次审核任务完成时间 < 60秒
- **并发能力**: 支持至少10个并发审核任务
- **准确率**: 各类检测准确率 > 85%

#### 2.2.3 配置需求
- **规则配置**: 支持审核规则的灵活配置和动态更新
- **模板管理**: 提示词模板的版本管理和A/B测试
- **阈值调整**: 支持检测阈值的精细化调整

## 3. 系统架构设计

### 3.1 整体架构图

```mermaid
graph TB
    subgraph "API接口层"
        A1[POST /api/verifyquestion<br/>审核启动接口]
        A2[GET /api/getverifystate<br/>状态查询接口]
    end

    subgraph "服务协调层"
        B1[QuestionVerificationService<br/>审核服务协调器]
        B2[TaskManager<br/>任务状态管理]
        B3[ConfigManager<br/>配置管理]
    end

    subgraph "Agent执行层 (LangGraph)"
        C1[ContentAgent<br/>内容审核]
        C2[ExpressionAgent<br/>表达规范]
        C3[AttributeAgent<br/>属性检测]
        C4[ComplianceAgent<br/>合规检测]
    end

    subgraph "核心支撑层"
        D1[LLMManager<br/>模型管理]
        D2[RuleEngine<br/>规则引擎]
        D3[DataStore<br/>敏感词库/规则库]
    end

    A1 --> B1
    A2 --> B2
    B1 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    B1 --> B2
    B1 --> B3
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C2 --> D2
    C4 --> D2
    D2 --> D3
```

### 3.2 LangGraph工作流设计

#### 3.2.1 状态管理结构
```python
class VerificationState(TypedDict):
    """审核状态数据结构"""
    task_id: str                    # 任务ID
    question_data: Dict[str, Any]   # 试题数据
    current_step: str               # 当前执行步骤
    steps_completed: List[str]      # 已完成步骤
    verification_results: List[VerificationStep]  # 审核结果
    error_info: Optional[str]       # 错误信息
    status_callback: Optional[Callable]  # 状态回调函数
```

#### 3.2.2 工作流执行图
```python
class VerificationWorkflow:
    """智能审核工作流"""

    def __init__(self):
        self.graph = StateGraph(VerificationState)
        self._build_workflow()

    def _build_workflow(self):
        """构建审核工作流"""
        # 添加Agent节点
        self.graph.add_node("content_verification", self._content_node)
        self.graph.add_node("expression_verification", self._expression_node)
        self.graph.add_node("attribute_verification", self._attribute_node)
        self.graph.add_node("compliance_verification", self._compliance_node)
        self.graph.add_node("result_aggregation", self._aggregation_node)

        # 定义执行流程
        self.graph.set_entry_point("content_verification")
        self.graph.add_edge("content_verification", "expression_verification")
        self.graph.add_edge("expression_verification", "attribute_verification")
        self.graph.add_edge("attribute_verification", "compliance_verification")
        self.graph.add_edge("compliance_verification", "result_aggregation")
        self.graph.add_edge("result_aggregation", END)

        # 添加条件分支（可选）
        self.graph.add_conditional_edges(
            "content_verification",
            self._should_continue,
            {
                "continue": "expression_verification",
                "stop": "result_aggregation"
            }
        )
```

### 3.3 Agent架构设计

#### 3.3.1 Agent基类设计
```python
class BaseVerificationAgent(ABC):
    """审核Agent基类"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.step_name = config.get("step_name", "未知步骤")
        self.llm_manager = LLMManager()
        self.rule_engine = RuleEngine()

    @abstractmethod
    async def verify(self, question_data: Dict[str, Any]) -> VerificationStep:
        """执行具体的审核逻辑"""
        pass

    async def execute(self, state: VerificationState) -> VerificationState:
        """执行审核并更新状态"""
        try:
            # 更新当前步骤
            state["current_step"] = self.step_name
            await self._notify_status(state, "processing")

            # 执行审核
            result = await self.verify(state["question_data"])

            # 更新结果
            state["verification_results"].append(result)
            state["steps_completed"].append(self.step_name)

            await self._notify_status(state, "completed")
            return state

        except Exception as e:
            state["error_info"] = f"{self.step_name}执行失败: {str(e)}"
            await self._notify_status(state, "failed")
            raise
```

## 4. API接口设计

### 4.1 接口规范

#### 4.1.1 审核启动接口

**接口路径**: `POST /api/verifyquestion`

**功能描述**: 启动试题智能审核任务，返回任务ID和初始状态

**请求参数**:
```json
{
    "QuesPost": {
        // 与现有 /AgentGen 接口一致的试题数据结构
        "QuesStr": "试题内容",
        "QuesType": "选择题",
        "QuesOptions": ["A选项", "B选项", "C选项", "D选项"],
        "QuesAnswer": "A",
        "QuesAnalysis": "解析内容",
        "AiQuesTypePost": {
            "QuesProps": [
                {
                    "QuesPropName": "知识点",
                    "QuesPropArr": ["函数", "导数"]
                },
                {
                    "QuesPropName": "难度层次",
                    "QuesPropArr": ["中等"]
                }
            ]
        }
    },
    "WorkflowPost": {
        // 可选的工作流配置
        "enabledSteps": ["content", "expression", "attribute", "compliance"]
    },
    "TaskName": "智能审核任务",
    "TaskId": "可选的自定义任务ID"
}
```

**响应结构**:
```json
{
    "code": 0,
    "msg": "审核任务已启动",
    "data": {
        "taskId": "uuid-generated-task-id",
        "status": "processing",
        "stepList": [
            {
                "stepName": "试题内容审核",
                "stepStatus": 1,  // 0:通过, 1:处理中, 2:失败
                "progress": 0,
                "failedItems": []
            },
            {
                "stepName": "表达规范检测",
                "stepStatus": 0,
                "progress": 0,
                "failedItems": []
            },
            {
                "stepName": "属性检测",
                "stepStatus": 0,
                "progress": 0,
                "failedItems": []
            },
            {
                "stepName": "合规性检测",
                "stepStatus": 0,
                "progress": 0,
                "failedItems": []
            }
        ]
    }
}
```

#### 4.1.2 状态查询接口

**接口路径**: `GET /api/getverifystate`

**功能描述**: 查询审核任务的实时状态和结果

**请求参数**:
```json
{
    "taskId": "uuid-task-id"
}
```

**响应结构**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "taskId": "uuid-task-id",
        "status": "completed",  // processing, completed, failed
        "overallResult": "passed",  // passed, failed, warning
        "totalSteps": 4,
        "completedSteps": 4,
        "stepList": [
            {
                "stepName": "试题内容审核",
                "stepStatus": 2,  // 0:通过, 1:处理中, 2:失败
                "progress": 100,
                "executionTime": 15.2,
                "failedItems": [
                    {
                        "itemName": "内容完整性检测",
                        "severity": "error",  // error, warning, info
                        "suggestionItems": [
                            {
                                "reason": "试题缺少标准答案",
                                "suggestion": "请补充完整的标准答案",
                                "inCorrect": "答案字段为空",
                                "correct": "应提供明确的答案选项",
                                "element": "QuesAnswer",
                                "confidence": 0.95
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

#### 4.1.3 错误响应格式

**通用错误响应**:
```json
{
    "code": 1001,
    "msg": "任务不存在或已过期",
    "data": null,
    "error": {
        "type": "TaskNotFoundError",
        "details": "Task ID: xxx not found in system"
    }
}
```

**错误码定义**:
- `1001`: 任务不存在
- `1002`: 参数验证失败
- `1003`: 系统繁忙，请稍后重试
- `1004`: 模型服务不可用
- `1005`: 配置错误

### 4.2 数据模型设计

#### 4.2.1 请求模型
```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum

class VerificationRequest(BaseModel):
    """智能审核请求模型"""
    QuesPost: QuesPost = Field(..., description="试题数据")
    WorkflowPost: Optional[WorkflowPost] = Field(None, description="工作流配置")
    TaskName: str = Field(default="智能审核任务", description="任务名称")
    TaskId: Optional[str] = Field(None, description="自定义任务ID")

class WorkflowPost(BaseModel):
    """工作流配置模型"""
    enabledSteps: List[str] = Field(
        default=["content", "expression", "attribute", "compliance"],
        description="启用的审核步骤"
    )
    parallelExecution: bool = Field(default=False, description="是否并行执行")
    timeoutSeconds: int = Field(default=300, description="超时时间(秒)")
```

#### 4.2.2 响应模型
```python
class SeverityLevel(str, Enum):
    """问题严重程度"""
    ERROR = "error"      # 错误，必须修改
    WARNING = "warning"  # 警告，建议修改
    INFO = "info"        # 信息，可选修改

class SuggestionItem(BaseModel):
    """审核建议项"""
    reason: str = Field(..., description="问题原因")
    suggestion: str = Field(..., description="修改建议")
    inCorrect: str = Field(..., description="错误内容")
    correct: str = Field(..., description="建议内容")
    element: str = Field(..., description="问题元素字段")
    confidence: float = Field(default=0.0, ge=0.0, le=1.0, description="置信度")
    position: Optional[Dict[str, Any]] = Field(None, description="问题位置信息")

class FailedItem(BaseModel):
    """审核失败项"""
    itemName: str = Field(..., description="检测项名称")
    severity: SeverityLevel = Field(..., description="问题严重程度")
    suggestionItems: List[SuggestionItem] = Field(..., description="具体建议列表")

class StepStatus(int, Enum):
    """步骤状态"""
    PASSED = 0      # 通过
    PROCESSING = 1  # 处理中
    FAILED = 2      # 失败

class VerificationStep(BaseModel):
    """审核步骤"""
    stepName: str = Field(..., description="步骤名称")
    stepStatus: StepStatus = Field(..., description="步骤状态")
    progress: int = Field(default=0, ge=0, le=100, description="进度百分比")
    executionTime: Optional[float] = Field(None, description="执行时间(秒)")
    failedItems: List[FailedItem] = Field(default=[], description="失败项列表")

class TaskStatus(str, Enum):
    """任务状态"""
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败
    CANCELLED = "cancelled"   # 已取消

class OverallResult(str, Enum):
    """总体结果"""
    PASSED = "passed"    # 通过
    FAILED = "failed"    # 失败
    WARNING = "warning"  # 有警告

class VerificationResponse(BaseModel):
    """审核响应模型"""
    code: int = Field(..., description="响应码")
    msg: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    error: Optional[Dict[str, Any]] = Field(None, description="错误信息")

class VerificationResult(BaseModel):
    """审核结果数据"""
    taskId: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    overallResult: Optional[OverallResult] = Field(None, description="总体结果")
    totalSteps: int = Field(..., description="总步骤数")
    completedSteps: int = Field(default=0, description="已完成步骤数")
    stepList: List[VerificationStep] = Field(..., description="步骤列表")
    createdAt: str = Field(..., description="创建时间")
    updatedAt: str = Field(..., description="更新时间")
```

#### 4.2.3 内部状态模型
```python
class VerificationState(TypedDict):
    """LangGraph状态模型"""
    task_id: str
    question_data: Dict[str, Any]
    current_step: str
    steps_completed: List[str]
    verification_results: List[VerificationStep]
    error_info: Optional[str]
    status_callback: Optional[Callable]
    config: Dict[str, Any]
    start_time: float
    metadata: Dict[str, Any]
```

## 5. Agent实现设计

### 5.1 内容审核Agent (ContentVerificationAgent)

#### 5.1.1 功能职责
- **结构完整性检测**: 验证试题是否包含必要的组成部分
- **逻辑一致性检测**: 检查题目、选项、答案、解析之间的逻辑关系
- **内容准确性检测**: 验证答案和解析的正确性

#### 5.1.2 检测规则
```python
class ContentVerificationAgent(BaseVerificationAgent):
    """内容审核Agent"""

    async def verify(self, question_data: Dict[str, Any]) -> VerificationStep:
        """执行内容审核"""
        failed_items = []

        # 1. 结构完整性检测
        structure_issues = await self._check_structure_completeness(question_data)
        if structure_issues:
            failed_items.append(FailedItem(
                itemName="结构完整性检测",
                severity=SeverityLevel.ERROR,
                suggestionItems=structure_issues
            ))

        # 2. 逻辑一致性检测
        logic_issues = await self._check_logic_consistency(question_data)
        if logic_issues:
            failed_items.append(FailedItem(
                itemName="逻辑一致性检测",
                severity=SeverityLevel.WARNING,
                suggestionItems=logic_issues
            ))

        # 3. 答案准确性检测
        accuracy_issues = await self._check_answer_accuracy(question_data)
        if accuracy_issues:
            failed_items.append(FailedItem(
                itemName="答案准确性检测",
                severity=SeverityLevel.ERROR,
                suggestionItems=accuracy_issues
            ))

        return VerificationStep(
            stepName="试题内容审核",
            stepStatus=StepStatus.FAILED if failed_items else StepStatus.PASSED,
            progress=100,
            failedItems=failed_items
        )
```

#### 5.1.3 配置参数
```yaml
verification:
  content_check:
    name: "试题内容审核"
    enabled: true
    timeout: 30
    rules:
      require_question: true      # 必须有题目
      require_options: true       # 选择题必须有选项
      require_answer: true        # 必须有答案
      require_analysis: false     # 解析可选
      min_options: 2             # 最少选项数
      max_options: 6             # 最多选项数
    prompts:
      structure_check: |
        请检查以下试题的结构完整性：
        题目：{question}
        选项：{options}
        答案：{answer}
        解析：{analysis}

        检查要点：
        1. 题目是否清晰完整
        2. 选择题是否有足够的选项
        3. 答案是否明确
        4. 解析是否与答案匹配
```

### 5.2 表达规范Agent (ExpressionVerificationAgent)

#### 5.2.1 功能职责
- **语言规范检测**: 错别字、语法错误、表达不当
- **格式规范检测**: 标点符号、数学公式、特殊符号
- **术语规范检测**: 专业术语的准确性和一致性

#### 5.2.2 实现策略
```python
class ExpressionVerificationAgent(BaseVerificationAgent):
    """表达规范审核Agent"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.typo_checker = TypoChecker(config.get("typo_dict_path"))
        self.punctuation_checker = PunctuationChecker(config.get("punctuation_rules"))

    async def verify(self, question_data: Dict[str, Any]) -> VerificationStep:
        """执行表达规范审核"""
        failed_items = []

        # 1. 错别字检测
        typo_issues = await self._check_typos(question_data)
        if typo_issues:
            failed_items.append(FailedItem(
                itemName="错别字检测",
                severity=SeverityLevel.WARNING,
                suggestionItems=typo_issues
            ))

        # 2. 标点符号检测
        punctuation_issues = await self._check_punctuation(question_data)
        if punctuation_issues:
            failed_items.append(FailedItem(
                itemName="标点符号检测",
                severity=SeverityLevel.INFO,
                suggestionItems=punctuation_issues
            ))

        # 3. 语法检测
        grammar_issues = await self._check_grammar(question_data)
        if grammar_issues:
            failed_items.append(FailedItem(
                itemName="语法检测",
                severity=SeverityLevel.WARNING,
                suggestionItems=grammar_issues
            ))

        return VerificationStep(
            stepName="表达规范检测",
            stepStatus=StepStatus.FAILED if failed_items else StepStatus.PASSED,
            progress=100,
            failedItems=failed_items
        )
```

#### 5.2.3 数据文件结构
```
data/
├── typo_dict.txt           # 错别字词典
├── punctuation_rules.json  # 标点符号规则
└── grammar_patterns.json   # 语法检测模式
```

### 5.3 属性检测Agent (AttributeVerificationAgent)

#### 5.3.1 功能职责
- **知识点匹配度检测**: 验证试题内容与标注知识点的匹配程度
- **难度层次评估**: 基于内容复杂度评估难度标注的合理性
- **考核目标验证**: 检查试题是否达到预期的考核目标

#### 5.3.2 实现逻辑
```python
class AttributeVerificationAgent(BaseVerificationAgent):
    """属性检测Agent"""

    async def verify(self, question_data: Dict[str, Any]) -> VerificationStep:
        """执行属性检测"""
        failed_items = []

        # 获取标注的属性信息
        ai_ques_type = question_data.get("AiQuesTypePost", {})
        ques_props = ai_ques_type.get("QuesProps", [])

        # 1. 知识点匹配度检测
        knowledge_issues = await self._check_knowledge_points(question_data, ques_props)
        if knowledge_issues:
            failed_items.append(FailedItem(
                itemName="知识点匹配度检测",
                severity=SeverityLevel.WARNING,
                suggestionItems=knowledge_issues
            ))

        # 2. 难度层次评估
        difficulty_issues = await self._check_difficulty_level(question_data, ques_props)
        if difficulty_issues:
            failed_items.append(FailedItem(
                itemName="难度层次评估",
                severity=SeverityLevel.INFO,
                suggestionItems=difficulty_issues
            ))

        return VerificationStep(
            stepName="属性检测",
            stepStatus=StepStatus.FAILED if failed_items else StepStatus.PASSED,
            progress=100,
            failedItems=failed_items
        )

    async def _check_knowledge_points(self, question_data: Dict, ques_props: List) -> List[SuggestionItem]:
        """检测知识点匹配度"""
        knowledge_props = [
            prop for prop in ques_props
            if prop.get("QuesPropName") in ["知识点", "考核点", "考核内容"]
        ]

        if not knowledge_props:
            return [SuggestionItem(
                reason="未标注知识点信息",
                suggestion="请为试题标注相关知识点",
                inCorrect="缺少知识点标注",
                correct="添加知识点属性",
                element="AiQuesTypePost.QuesProps",
                confidence=1.0
            )]

        # 使用LLM分析知识点匹配度
        # ... 具体实现
```

### 5.4 合规性检测Agent (ComplianceVerificationAgent)

#### 5.4.1 功能职责
- **敏感内容检测**: 政治敏感、暴力、歧视等不当内容
- **教育适宜性检测**: 符合教育价值观和年龄适宜性
- **版权合规检测**: 避免侵权内容和不当引用

#### 5.4.2 实现策略
```python
class ComplianceVerificationAgent(BaseVerificationAgent):
    """合规性检测Agent"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.sensitive_words_checker = SensitiveWordsChecker(
            config.get("sensitive_words_path")
        )

    async def verify(self, question_data: Dict[str, Any]) -> VerificationStep:
        """执行合规性检测"""
        failed_items = []

        # 1. 敏感词检测
        sensitive_issues = await self._check_sensitive_words(question_data)
        if sensitive_issues:
            failed_items.append(FailedItem(
                itemName="敏感内容检测",
                severity=SeverityLevel.ERROR,
                suggestionItems=sensitive_issues
            ))

        # 2. 教育适宜性检测
        education_issues = await self._check_educational_appropriateness(question_data)
        if education_issues:
            failed_items.append(FailedItem(
                itemName="教育适宜性检测",
                severity=SeverityLevel.WARNING,
                suggestionItems=education_issues
            ))

        return VerificationStep(
            stepName="合规性检测",
            stepStatus=StepStatus.FAILED if failed_items else StepStatus.PASSED,
            progress=100,
            failedItems=failed_items
        )
```

#### 5.4.3 敏感词库结构
```
data/
├── sensitive_words/
│   ├── political.txt      # 政治敏感词
│   ├── violence.txt       # 暴力相关词汇
│   ├── discrimination.txt # 歧视性词汇
│   └── inappropriate.txt  # 不当内容词汇
```

## 6. 配置文件设计

### 6.1 完整配置结构
```yaml
# config.yaml 扩展部分
verification:
  # 全局配置
  global:
    enabled: true
    default_timeout: 300
    max_concurrent_tasks: 10
    result_cache_ttl: 3600

  # 审核步骤配置
  steps:
    content_check:
      name: "试题内容审核"
      enabled: true
      timeout: 30
      weight: 0.4  # 权重，用于计算总体评分
      rules:
        require_question: true
        require_options: true
        require_answer: true
        require_analysis: false
        min_options: 2
        max_options: 6
        check_logic_consistency: true
        check_answer_accuracy: true

    expression_check:
      name: "表达规范检测"
      enabled: true
      timeout: 20
      weight: 0.2
      rules:
        check_typos: true
        check_grammar: true
        check_punctuation: true
        check_terminology: true
      data_files:
        typo_dict: "data/typo_dict.txt"
        punctuation_rules: "data/punctuation_rules.json"
        grammar_patterns: "data/grammar_patterns.json"

    attribute_check:
      name: "属性检测"
      enabled: true
      timeout: 15
      weight: 0.2
      rules:
        check_knowledge_points: true
        check_difficulty_level: true
        check_exam_objectives: true
        knowledge_point_threshold: 0.7  # 匹配度阈值
        difficulty_tolerance: 1  # 难度层次容差

    compliance_check:
      name: "合规性检测"
      enabled: true
      timeout: 25
      weight: 0.2
      rules:
        check_sensitive_words: true
        check_educational_appropriateness: true
        check_copyright_compliance: true
      data_files:
        sensitive_words_dir: "data/sensitive_words/"
        update_interval: 86400  # 24小时更新一次

  # LLM配置
  llm:
    default_model: "deepseek-chat"
    temperature: 0.1
    max_tokens: 2000
    retry_attempts: 3
    retry_delay: 1.0

  # 提示词模板
  prompts:
    content_structure_check: |
      你是专业的试题内容审核专家。请检查以下试题的结构完整性：

      题目：{question}
      选项：{options}
      答案：{answer}
      解析：{analysis}

      检查要点：
      1. 题目是否清晰完整，表述准确
      2. 选择题选项是否充分且合理
      3. 答案是否明确正确
      4. 解析是否与答案匹配，逻辑清晰

      请以JSON格式返回检查结果。

    expression_grammar_check: |
      你是专业的语言表达审核专家。请检查以下内容的语言表达规范：

      内容：{content}

      检查要点：
      1. 是否存在错别字或拼写错误
      2. 语法是否正确
      3. 标点符号使用是否规范
      4. 专业术语使用是否准确

      请以JSON格式返回检查结果。

    attribute_knowledge_check: |
      你是专业的试题属性审核专家。请分析试题内容与标注属性的匹配度：

      试题内容：{question_content}
      标注知识点：{knowledge_points}
      标注难度：{difficulty_level}

      请评估：
      1. 试题内容是否与标注知识点匹配
      2. 试题难度是否与标注难度层次一致
      3. 给出匹配度评分（0-1）

      请以JSON格式返回分析结果。

    compliance_sensitive_check: |
      你是专业的内容合规审核专家。请检查以下内容是否存在合规问题：

      内容：{content}

      检查要点：
      1. 是否包含政治敏感内容
      2. 是否包含暴力或不当内容
      3. 是否存在歧视性表述
      4. 是否符合教育价值观

      请以JSON格式返回检查结果。
```

## 7. 实施计划

### 7.1 开发阶段规划

#### 阶段1: 基础架构搭建 (1-2周)
- **任务**:
  - 扩展数据模型定义
  - 配置文件结构设计
  - Agent基类实现
  - LangGraph工作流框架搭建
- **交付物**:
  - 完整的数据模型代码
  - 配置文件模板
  - Agent基类和工作流框架
- **验收标准**:
  - 代码通过单元测试
  - 配置文件可正常加载
  - 工作流可正常初始化

#### 阶段2: 核心Agent实现 (2-3周)
- **任务**:
  - 内容审核Agent开发
  - 表达规范Agent开发
  - 属性检测Agent开发
  - 合规性检测Agent开发
- **交付物**:
  - 四个核心Agent的完整实现
  - 相关数据文件和规则库
- **验收标准**:
  - 每个Agent可独立运行
  - 检测结果格式正确
  - 基本功能测试通过

#### 阶段3: API接口开发 (1周)
- **任务**:
  - 审核启动接口实现
  - 状态查询接口实现
  - 错误处理和响应格式统一
- **交付物**:
  - 完整的API接口代码
  - API文档和测试用例
- **验收标准**:
  - 接口功能正常
  - 响应格式符合规范
  - 错误处理完善

#### 阶段4: 集成测试与优化 (1-2周)
- **任务**:
  - 端到端集成测试
  - 性能优化和调试
  - 配置参数调优
- **交付物**:
  - 集成测试报告
  - 性能优化报告
  - 部署文档
- **验收标准**:
  - 系统稳定运行
  - 性能指标达标
  - 准确率满足要求

### 7.2 技术风险与应对策略

#### 7.2.1 性能风险
**风险描述**: 多Agent串行执行可能导致响应时间过长

**应对策略**:
- 实现Agent并行执行机制
- 添加超时控制和熔断机制
- 使用缓存减少重复计算
- 优化LLM调用策略

#### 7.2.2 准确性风险
**风险描述**: LLM检测结果可能存在误判或不稳定

**应对策略**:
- 结合规则引擎提高准确性
- 实现多模型投票机制
- 添加置信度评估
- 建立人工校验反馈机制

#### 7.2.3 扩展性风险
**风险描述**: 新增审核规则和Agent的配置复杂度

**应对策略**:
- 设计灵活的配置系统
- 实现热更新机制
- 提供配置验证工具
- 建立配置版本管理

### 7.3 质量保证措施

#### 7.3.1 测试策略
- **单元测试**: 每个Agent和工具类的独立测试
- **集成测试**: 工作流端到端测试
- **性能测试**: 并发和压力测试
- **准确性测试**: 基于标准数据集的准确率评估

#### 7.3.2 监控指标
- **性能指标**: 响应时间、吞吐量、资源使用率
- **质量指标**: 准确率、召回率、误报率
- **业务指标**: 任务成功率、用户满意度

## 8. 核心代码实现

### 8.1 项目目录结构
```
app/
├── api/
│   └── verification.py              # 审核API接口
├── services/
│   ├── verification_service.py      # 审核服务协调器
│   └── agents/
│       ├── __init__.py
│       ├── base_agent.py            # Agent基类
│       ├── content_agent.py         # 内容审核Agent
│       ├── expression_agent.py      # 表达规范Agent
│       ├── attribute_agent.py       # 属性检测Agent
│       └── compliance_agent.py      # 合规性检测Agent
├── models/
│   └── verification_schemas.py      # 审核相关数据模型
├── utils/
│   ├── sensitive_words_checker.py   # 敏感词检测工具
│   ├── typo_checker.py             # 错别字检测工具
│   └── verification_utils.py        # 审核工具函数
├── data/
│   ├── sensitive_words/             # 敏感词库目录
│   ├── typo_dict.txt               # 错别字词典
│   └── punctuation_rules.json      # 标点符号规则
└── config/
    └── verification_config.yaml     # 审核配置文件
```

### 8.2 服务协调器实现
```python
# app/services/verification_service.py
from typing import Dict, Any, Optional, Callable
from langgraph.graph import StateGraph, END
from app.core.task_manager import task_manager
from app.core.llm_manager import LLMManager
from app.models.verification_schemas import *
from app.services.agents import *

class QuestionVerificationService:
    """试题审核服务协调器"""

    def __init__(self):
        self.workflow = VerificationWorkflow()
        self.task_manager = task_manager
        self.config = config_manager.get("verification", {})

    async def start_verification(
        self,
        request: VerificationRequest
    ) -> VerificationResponse:
        """启动审核任务"""
        try:
            # 生成任务ID
            task_id = request.TaskId or str(uuid.uuid4())

            # 初始化任务状态
            initial_result = VerificationResult(
                taskId=task_id,
                status=TaskStatus.PROCESSING,
                totalSteps=4,
                completedSteps=0,
                stepList=self._create_initial_steps(),
                createdAt=datetime.now().isoformat(),
                updatedAt=datetime.now().isoformat()
            )

            # 保存初始状态
            await self.task_manager.save_task_state(task_id, initial_result.dict())

            # 异步执行审核工作流
            asyncio.create_task(self._execute_verification(task_id, request))

            return VerificationResponse(
                code=0,
                msg="审核任务已启动",
                data=initial_result.dict()
            )

        except Exception as e:
            return VerificationResponse(
                code=1003,
                msg="启动审核任务失败",
                error={"type": "ServiceError", "details": str(e)}
            )

    async def get_verification_state(self, task_id: str) -> VerificationResponse:
        """获取审核状态"""
        try:
            task_state = await self.task_manager.get_task_state(task_id)
            if not task_state:
                return VerificationResponse(
                    code=1001,
                    msg="任务不存在或已过期",
                    error={"type": "TaskNotFoundError", "details": f"Task ID: {task_id}"}
                )

            return VerificationResponse(
                code=0,
                msg="success",
                data=task_state
            )

        except Exception as e:
            return VerificationResponse(
                code=1003,
                msg="查询任务状态失败",
                error={"type": "ServiceError", "details": str(e)}
            )

    async def _execute_verification(self, task_id: str, request: VerificationRequest):
        """执行审核工作流"""
        try:
            # 创建状态回调函数
            async def status_callback(step_name: str, status: str, result: Optional[VerificationStep] = None):
                await self._update_task_status(task_id, step_name, status, result)

            # 初始化工作流状态
            initial_state = VerificationState(
                task_id=task_id,
                question_data=request.QuesPost.dict(),
                current_step="",
                steps_completed=[],
                verification_results=[],
                error_info=None,
                status_callback=status_callback,
                config=self.config,
                start_time=time.time(),
                metadata={"task_name": request.TaskName}
            )

            # 执行工作流
            final_state = await self.workflow.ainvoke(initial_state)

            # 更新最终状态
            await self._finalize_task(task_id, final_state)

        except Exception as e:
            await self._handle_task_error(task_id, str(e))

    def _create_initial_steps(self) -> List[VerificationStep]:
        """创建初始步骤列表"""
        return [
            VerificationStep(
                stepName="试题内容审核",
                stepStatus=StepStatus.PROCESSING,
                progress=0,
                failedItems=[]
            ),
            VerificationStep(
                stepName="表达规范检测",
                stepStatus=StepStatus.PASSED,
                progress=0,
                failedItems=[]
            ),
            VerificationStep(
                stepName="属性检测",
                stepStatus=StepStatus.PASSED,
                progress=0,
                failedItems=[]
            ),
            VerificationStep(
                stepName="合规性检测",
                stepStatus=StepStatus.PASSED,
                progress=0,
                failedItems=[]
            )
        ]
```

### 8.3 LangGraph工作流实现
```python
# app/services/verification_service.py (续)
class VerificationWorkflow:
    """审核工作流"""

    def __init__(self):
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建审核工作流图"""
        workflow = StateGraph(VerificationState)

        # 添加Agent节点
        workflow.add_node("content_verification", self._content_node)
        workflow.add_node("expression_verification", self._expression_node)
        workflow.add_node("attribute_verification", self._attribute_node)
        workflow.add_node("compliance_verification", self._compliance_node)

        # 定义执行流程
        workflow.set_entry_point("content_verification")
        workflow.add_edge("content_verification", "expression_verification")
        workflow.add_edge("expression_verification", "attribute_verification")
        workflow.add_edge("attribute_verification", "compliance_verification")
        workflow.add_edge("compliance_verification", END)

        return workflow.compile()

    async def _content_node(self, state: VerificationState) -> VerificationState:
        """内容审核节点"""
        agent = ContentVerificationAgent(config_manager.get("verification.steps.content_check"))
        return await agent.execute(state)

    async def _expression_node(self, state: VerificationState) -> VerificationState:
        """表达规范检测节点"""
        agent = ExpressionVerificationAgent(config_manager.get("verification.steps.expression_check"))
        return await agent.execute(state)

    async def _attribute_node(self, state: VerificationState) -> VerificationState:
        """属性检测节点"""
        agent = AttributeVerificationAgent(config_manager.get("verification.steps.attribute_check"))
        return await agent.execute(state)

    async def _compliance_node(self, state: VerificationState) -> VerificationState:
        """合规性检测节点"""
        agent = ComplianceVerificationAgent(config_manager.get("verification.steps.compliance_check"))
        return await agent.execute(state)
```

### 8.4 API接口实现
```python
# app/api/verification.py
from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.models.verification_schemas import *
from app.services.verification_service import QuestionVerificationService

router = APIRouter(prefix="/api", tags=["verification"])
verification_service = QuestionVerificationService()

@router.post("/verifyquestion", response_model=VerificationResponse)
async def verify_question(
    request: VerificationRequest,
    background_tasks: BackgroundTasks
):
    """启动试题审核任务"""
    try:
        result = await verification_service.start_verification(request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"启动审核任务失败: {str(e)}"
        )

@router.get("/getverifystate", response_model=VerificationResponse)
async def get_verify_state(task_id: str):
    """查询审核任务状态"""
    try:
        result = await verification_service.get_verification_state(task_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )

@router.delete("/cancelverify")
async def cancel_verification(task_id: str):
    """取消审核任务"""
    try:
        result = await verification_service.cancel_verification(task_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"取消任务失败: {str(e)}"
        )
```

### 8.5 工具类实现
```python
# app/utils/sensitive_words_checker.py
import re
from typing import List, Dict, Set
from pathlib import Path

class SensitiveWordsChecker:
    """敏感词检测工具"""

    def __init__(self, words_dir: str):
        self.words_dir = Path(words_dir)
        self.sensitive_words: Set[str] = set()
        self.patterns: List[re.Pattern] = []
        self._load_words()

    def _load_words(self):
        """加载敏感词库"""
        for file_path in self.words_dir.glob("*.txt"):
            with open(file_path, 'r', encoding='utf-8') as f:
                words = [line.strip() for line in f if line.strip()]
                self.sensitive_words.update(words)

        # 编译正则表达式模式
        self.patterns = [re.compile(word) for word in self.sensitive_words]

    def check_text(self, text: str) -> List[Dict[str, Any]]:
        """检测文本中的敏感词"""
        found_words = []

        for pattern in self.patterns:
            matches = pattern.finditer(text)
            for match in matches:
                found_words.append({
                    "word": match.group(),
                    "start": match.start(),
                    "end": match.end(),
                    "context": text[max(0, match.start()-10):match.end()+10]
                })

        return found_words

# app/utils/typo_checker.py
class TypoChecker:
    """错别字检测工具"""

    def __init__(self, dict_path: str):
        self.dict_path = dict_path
        self.typo_dict: Dict[str, str] = {}
        self._load_dict()

    def _load_dict(self):
        """加载错别字词典"""
        with open(self.dict_path, 'r', encoding='utf-8') as f:
            for line in f:
                if '\t' in line:
                    wrong, correct = line.strip().split('\t', 1)
                    self.typo_dict[wrong] = correct

    def check_text(self, text: str) -> List[Dict[str, Any]]:
        """检测文本中的错别字"""
        typos = []

        for wrong_word, correct_word in self.typo_dict.items():
            if wrong_word in text:
                typos.append({
                    "wrong": wrong_word,
                    "correct": correct_word,
                    "positions": [i for i in range(len(text)) if text.startswith(wrong_word, i)]
                })

        return typos
```

## 9. 部署与运维

### 9.1 部署要求

#### 9.1.1 环境依赖
```bash
# Python版本要求
Python >= 3.10

# 核心依赖包
fastapi >= 0.104.0
langchain >= 0.1.0
langgraph >= 0.0.40
pydantic >= 2.0.0
uvicorn >= 0.24.0
asyncio-mqtt >= 0.13.0

# 新增依赖
aiofiles >= 23.0.0      # 异步文件操作
python-multipart >= 0.0.6  # 文件上传支持
```

#### 9.1.2 配置文件部署
```bash
# 创建配置目录
mkdir -p config data/sensitive_words

# 复制配置文件
cp config/verification_config.yaml config/
cp data/sensitive_words/* data/sensitive_words/
cp data/typo_dict.txt data/
```

#### 9.1.3 启动命令
```bash
# 开发环境
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 生产环境
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 9.2 监控与日志

#### 9.2.1 性能监控指标
```python
# app/utils/metrics.py
from prometheus_client import Counter, Histogram, Gauge
import time

# 定义监控指标
verification_requests_total = Counter(
    'verification_requests_total',
    'Total verification requests',
    ['step_name', 'status']
)

verification_duration_seconds = Histogram(
    'verification_duration_seconds',
    'Verification duration in seconds',
    ['step_name']
)

active_verification_tasks = Gauge(
    'active_verification_tasks',
    'Number of active verification tasks'
)

class MetricsCollector:
    """指标收集器"""

    @staticmethod
    def record_request(step_name: str, status: str):
        verification_requests_total.labels(
            step_name=step_name,
            status=status
        ).inc()

    @staticmethod
    def record_duration(step_name: str, duration: float):
        verification_duration_seconds.labels(
            step_name=step_name
        ).observe(duration)

    @staticmethod
    def set_active_tasks(count: int):
        active_verification_tasks.set(count)
```

#### 9.2.2 日志配置
```python
# app/utils/logger.py
import logging
import json
from datetime import datetime

class VerificationLogger:
    """审核专用日志器"""

    def __init__(self):
        self.logger = logging.getLogger("verification")
        self.logger.setLevel(logging.INFO)

        # 创建文件处理器
        handler = logging.FileHandler("logs/verification.log")
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def log_verification_start(self, task_id: str, question_data: dict):
        """记录审核开始"""
        self.logger.info(json.dumps({
            "event": "verification_start",
            "task_id": task_id,
            "timestamp": datetime.now().isoformat(),
            "question_type": question_data.get("QuesType"),
            "question_length": len(question_data.get("QuesStr", ""))
        }))

    def log_step_completion(self, task_id: str, step_name: str,
                          duration: float, status: str, issues_count: int):
        """记录步骤完成"""
        self.logger.info(json.dumps({
            "event": "step_completion",
            "task_id": task_id,
            "step_name": step_name,
            "duration": duration,
            "status": status,
            "issues_count": issues_count,
            "timestamp": datetime.now().isoformat()
        }))
```

### 9.3 扩展性设计

#### 9.3.1 新增Agent的步骤
1. **创建Agent类**: 继承`BaseVerificationAgent`
2. **实现verify方法**: 定义具体的检测逻辑
3. **添加配置**: 在`verification_config.yaml`中添加配置
4. **注册到工作流**: 在`VerificationWorkflow`中添加节点
5. **更新API**: 如需要，更新响应模型

#### 9.3.2 配置热更新机制
```python
# app/utils/config_watcher.py
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigWatcher(FileSystemEventHandler):
    """配置文件监控器"""

    def __init__(self, config_manager):
        self.config_manager = config_manager

    def on_modified(self, event):
        if event.src_path.endswith('.yaml'):
            asyncio.create_task(self.config_manager.reload_config())
```

## 10. 总结与展望

### 10.1 技术方案总结

本智能审核功能技术方案基于现有的智能命题系统架构，充分利用了FastAPI + LangChain + LangGraph的技术栈优势，设计了一套完整的多Agent协作审核系统。

#### 10.1.1 核心优势
- **架构兼容性**: 完全兼容现有系统架构，无需大规模重构
- **模块化设计**: 每个Agent职责明确，便于独立开发和测试
- **配置化管理**: 通过YAML配置文件灵活管理审核规则和参数
- **异步处理**: 支持高并发审核任务，提升系统性能
- **扩展性强**: 易于添加新的审核Agent和检测规则

#### 10.1.2 技术创新点
- **LangGraph工作流**: 使用状态图管理复杂的审核流程
- **多层次检测**: 结合LLM智能分析和规则引擎精确检测
- **实时状态更新**: 提供细粒度的任务进度追踪
- **置信度评估**: 为每个检测结果提供可信度评分

### 10.2 预期效果

#### 10.2.1 功能效果
- **检测覆盖率**: 覆盖内容、表达、属性、合规四大维度
- **检测准确率**: 预期各类检测准确率达到85%以上
- **响应性能**: 单次完整审核时间控制在60秒以内
- **并发能力**: 支持至少10个并发审核任务

#### 10.2.2 业务价值
- **提升效率**: 自动化审核减少人工审核工作量70%以上
- **保证质量**: 标准化检测流程确保审核质量一致性
- **降低风险**: 多维度合规检测降低内容风险
- **用户体验**: 实时反馈和建议提升用户使用体验

### 10.3 后续发展规划

#### 10.3.1 短期优化 (1-3个月)
- **性能优化**: 优化LLM调用策略，提升响应速度
- **准确率提升**: 基于实际使用数据优化检测规则
- **用户界面**: 开发审核结果可视化界面
- **批量处理**: 支持批量试题审核功能

#### 10.3.2 中期扩展 (3-6个月)
- **智能学习**: 基于用户反馈持续优化检测模型
- **多学科支持**: 扩展支持不同学科的专业审核规则
- **协作审核**: 支持人工审核员与AI协作的混合审核模式
- **审核报告**: 生成详细的审核质量分析报告

#### 10.3.3 长期愿景 (6-12个月)
- **自适应审核**: 基于历史数据自动调整审核策略
- **多模态支持**: 支持图片、音频等多媒体内容审核
- **国际化**: 支持多语言试题审核
- **开放平台**: 提供API接口供第三方系统集成

### 10.4 风险控制与应对

#### 10.4.1 技术风险控制
- **降级策略**: 当AI审核失败时自动切换到规则审核
- **监控告警**: 实时监控系统性能和准确率指标
- **版本管理**: 配置和模型的版本化管理，支持快速回滚
- **容错机制**: 单个Agent失败不影响整体审核流程

#### 10.4.2 业务风险应对
- **人工复核**: 对高风险内容提供人工复核机制
- **用户反馈**: 建立用户反馈渠道持续改进系统
- **合规更新**: 定期更新敏感词库和合规规则
- **培训支持**: 为用户提供系统使用培训和技术支持

### 10.5 实施建议

#### 10.5.1 开发建议
- **分阶段实施**: 按照既定的开发阶段逐步推进
- **测试驱动**: 采用TDD方法确保代码质量
- **持续集成**: 建立CI/CD流水线自动化部署
- **文档完善**: 维护完整的技术文档和用户手册

#### 10.5.2 运维建议
- **监控体系**: 建立完善的监控和告警体系
- **性能调优**: 定期进行性能分析和优化
- **安全防护**: 加强系统安全防护和数据保护
- **备份恢复**: 建立数据备份和灾难恢复机制

---

**本技术方案为智能命题系统的智能审核功能提供了完整的设计和实施指导，通过科学的架构设计和详细的实施计划，确保功能的成功交付和稳定运行。**
