# 智能审核功能技术方案

## 1. 项目现状分析

### 1.1 现有架构
- **框架**: FastAPI + LangChain + LangGraph
- **模型管理**: 支持多种LLM模型（DeepSeek、GPT-4、Qwen等）
- **任务管理**: 基于异步的任务状态管理和流式输出
- **配置管理**: 基于YAML的配置文件管理
- **API结构**: RESTful API设计，支持智能命题、试题克隆、模型交互

### 1.2 现有核心模块
- `app/api/`: API路由层
- `app/services/`: 业务服务层
- `app/core/`: 核心功能（状态管理）
- `app/models/`: 数据模型定义
- `app/utils/`: 工具类

## 2. 智能审核功能需求分析

### 2.1 业务需求
1. **试题内容审核**: 完整性检测、参考答案检测、解析检测
2. **表达规范检测**: 错别字、拼写错误、标点符号检测
3. **属性检测**: 知识点标注、难度层次检测
4. **合规性检测**: 敏感词、教育公平性、命题规范检测

### 2.2 技术需求
1. **多Agent架构**: 使用LangGraph构建可扩展的Agent系统
2. **异步处理**: 支持并发审核和状态轮询
3. **配置化**: 通过config.yaml配置审核规则和提示词
4. **状态管理**: 实时更新审核进度和结果

## 3. 技术架构设计

### 3.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    智能审核系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  API Layer                                                  │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ /verifyquestion │  │ /getverifystate                 │   │
│  │ (审核接口)       │  │ (状态查询接口)                   │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           QuestionVerificationService                   │ │
│  │              (审核服务协调器)                            │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Agent Layer (LangGraph)                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ ContentAgent│ │ExpressionAgt│ │AttributeAgt │ │CompliAgt│ │
│  │ (内容审核)   │ │ (表达规范)   │ │ (属性检测)   │ │(合规检测)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Core Layer                                                 │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │ TaskManager     │  │ LLMManager                      │   │
│  │ (任务状态管理)   │  │ (模型管理)                       │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Agent架构设计

基于LangGraph的多Agent系统：

```python
# 审核工作流
class VerificationWorkflow:
    def __init__(self):
        self.graph = StateGraph(VerificationState)
        self._build_graph()
    
    def _build_graph(self):
        # 添加节点
        self.graph.add_node("content_check", self.content_agent)
        self.graph.add_node("expression_check", self.expression_agent)
        self.graph.add_node("attribute_check", self.attribute_agent)
        self.graph.add_node("compliance_check", self.compliance_agent)
        
        # 定义流程
        self.graph.set_entry_point("content_check")
        self.graph.add_edge("content_check", "expression_check")
        self.graph.add_edge("expression_check", "attribute_check")
        self.graph.add_edge("attribute_check", "compliance_check")
        self.graph.add_edge("compliance_check", END)
```

## 4. 详细设计

### 4.1 API接口设计

#### 4.1.1 审核接口 `/api/verifyquestion`

**请求参数**: 与`/AgentGen`接口一致的试题数据

**响应结构**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "taskId": "string",
        "stepList": [
            {
                "stepName": "试题内容审核",
                "stepStatus": 0,
                "failedItems": [
                    {
                        "itemName": "内容完整性检测",
                        "suggestionItems": [
                            {
                                "reason": "试题缺少参考答案",
                                "suggestion": "请补充完整的参考答案",
                                "inCorrect": "缺失内容",
                                "correct": "建议内容",
                                "element": "QuesStr"
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

#### 4.1.2 状态查询接口 `/api/getverifystate`

**请求参数**:
```json
{
    "taskId": "uuid-string"
}
```

**响应结构**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "taskId": "string",
        "stepList": [
            {
                "stepName": "试题内容审核",
                "stepStatus": 0,
                "failedItems": [
                    {
                        "itemName": "内容完整性检测",
                        "suggestionItems": [
                            {
                                "reason": "试题缺少参考答案",
                                "suggestion": "请补充完整的参考答案",
                                "inCorrect": "缺失内容",
                                "correct": "建议内容",
                                "element": "QuesStr"
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

### 4.2 数据模型设计

```python
class VerificationRequest(BaseModel):
    """审核请求模型"""
    QuesPost: QuesPost
    WorkflowPost: Optional[WorkflowPost] = None
    AgentPost: Optional[AgentPost] = None
    TaskName: str = "智能审核"
    TaskId: Optional[str] = None

class SuggestionItem(BaseModel):
    """审核建议项"""
    reason: str
    suggestion: str
    inCorrect: str
    correct: str
    element: str

class FailedItem(BaseModel):
    """审核失败项"""
    itemName: str
    suggestionItems: List[SuggestionItem]

class VerificationStep(BaseModel):
    """审核步骤"""
    stepName: str
    stepStatus: int  # 0: 通过, 1: 失败
    failedItems: List[FailedItem]

class VerificationResponse(BaseModel):
    """审核响应模型"""
    code: int
    msg: str
    data: Dict[str, Any]
```

### 4.3 Agent实现设计

#### 4.3.1 内容审核Agent
- **功能**: 检测试题内容完整性、参考答案、解析等
- **实现**: 基于LLM的结构化分析
- **配置**: 可通过config.yaml配置检测规则

#### 4.3.2 表达规范Agent
- **功能**: 检测错别字、语法错误、标点符号
- **实现**: LLM + 规则引擎结合
- **配置**: 支持自定义检测规则,可通过config.yaml配置检测规则，错别字库和标点符号库应该是一个本地的文件

#### 4.3.3 属性检测Agent
- **功能**: 验证知识点标注、难度层次
- **实现**: 基于LLM的语义分析
- **配置**: 支持学科特定的属性规则,主要依靠传输的QuesPost中的AiQuesTypePost中的QuesProps中的QuesPropName为（考核点、考核内容、知识点或难度层次）的QuesPropArr列表中属性值进行判断

#### 4.3.4 合规性检测Agent
- **功能**: 敏感词检测、教育公平性检测
- **实现**: 传统词库 + LLM智能检测
- **配置**: 支持敏感词库更新和规则配置,可通过config.yaml配置检测规则，敏感词库应该是一个本地的文件

## 5. 实施计划

### 5.1 开发阶段
1. **阶段1**: 数据模型和配置扩展
2. **阶段2**: Agent架构实现
3. **阶段3**: API接口开发
4. **阶段4**: 敏感词库集成
5. **阶段5**: 测试和优化

### 5.2 技术风险
1. **性能风险**: 多Agent并发可能影响响应时间
2. **准确性风险**: LLM检测结果可能存在误判
3. **扩展性风险**: 新增审核规则的配置复杂度

### 5.3 解决方案
1. **性能优化**: 使用异步处理和缓存机制
2. **准确性提升**: 结合规则引擎和人工校验
3. **扩展性保证**: 设计灵活的配置系统

## 6. 配置文件扩展

需要在config.yaml中添加以下配置：

```yaml
# 智能审核配置
verification:
  # 审核步骤配置
  steps:
    content_check:
      name: "试题内容审核"
      enabled: true
      timeout: 30
    expression_check:
      name: "表达规范检测"
      enabled: true
      timeout: 20
    attribute_check:
      name: "属性检测"
      enabled: true
      timeout: 15
    compliance_check:
      name: "合规性检测"
      enabled: true
      timeout: 25
  
  # 敏感词库配置
  sensitive_words:
    enabled: true
    database_path: "data/sensitive_words.txt"
    update_interval: 86400  # 24小时
  
  # 审核提示词模板
  prompts:
    content_check: |
      你是专业的试题内容审核专家...
    expression_check: |
      你是专业的语言表达审核专家...
    attribute_check: |
      你是专业的试题属性审核专家...
    compliance_check: |
      你是专业的合规性审核专家...
```

## 7. 核心代码结构设计

### 7.1 目录结构
```
app/
├── api/
│   └── verification.py          # 审核API接口
├── services/
│   ├── verification_service.py  # 审核服务协调器
│   └── agents/
│       ├── __init__.py
│       ├── base_agent.py        # Agent基类
│       ├── content_agent.py     # 内容审核Agent
│       ├── expression_agent.py  # 表达规范Agent
│       ├── attribute_agent.py   # 属性检测Agent
│       └── compliance_agent.py  # 合规性检测Agent
├── models/
│   └── verification_schemas.py  # 审核相关数据模型
├── utils/
│   ├── sensitive_words.py       # 敏感词检测工具
│   └── verification_utils.py    # 审核工具函数
└── data/
    └── sensitive_words.txt      # 敏感词库文件
```

### 7.2 核心服务实现框架

```python
# app/services/verification_service.py
class QuestionVerificationService:
    """试题审核服务协调器"""

    def __init__(self):
        self.workflow = VerificationWorkflow()
        self.task_manager = task_manager

    async def verify_question(
        self,
        ques_post: Dict[str, Any],
        task_id: str,
        status_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """执行试题审核"""

        # 初始化审核状态
        verification_state = VerificationState(
            question_data=ques_post,
            task_id=task_id,
            steps=[]
        )

        # 执行审核工作流
        result = await self.workflow.ainvoke(
            verification_state,
            config={"callbacks": [status_callback] if status_callback else []}
        )

        return result
```

### 7.3 Agent基类设计

```python
# app/services/agents/base_agent.py
class BaseVerificationAgent:
    """审核Agent基类"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.llm_manager = LLMManager()
        self.step_name = config.get("step_name", "未知步骤")

    async def execute(
        self,
        state: VerificationState,
        **kwargs
    ) -> VerificationState:
        """执行审核逻辑"""
        try:
            # 更新状态为处理中
            await self._update_status(state, "processing")

            # 执行具体审核逻辑
            result = await self._verify(state)

            # 更新审核结果
            state.steps.append(result)

            # 更新状态为完成
            await self._update_status(state, "completed")

            return state

        except Exception as e:
            # 更新状态为失败
            await self._update_status(state, "failed", str(e))
            raise

    async def _verify(self, state: VerificationState) -> VerificationStep:
        """子类需要实现的具体审核逻辑"""
        raise NotImplementedError

    async def _update_status(
        self,
        state: VerificationState,
        status: str,
        message: str = ""
    ):
        """更新任务状态"""
        if hasattr(state, 'status_callback') and state.status_callback:
            await state.status_callback(
                task_id=state.task_id,
                step_name=self.step_name,
                status=status,
                message=message
            )
```

### 7.4 LangGraph工作流实现

```python
# app/services/verification_service.py
from langgraph.graph import StateGraph, END
from typing_extensions import TypedDict

class VerificationState(TypedDict):
    """审核状态数据结构"""
    question_data: Dict[str, Any]
    task_id: str
    steps: List[VerificationStep]
    current_step: str
    status_callback: Optional[Callable]

class VerificationWorkflow:
    """审核工作流"""

    def __init__(self):
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """构建审核工作流图"""
        workflow = StateGraph(VerificationState)

        # 添加Agent节点
        workflow.add_node("content_check", self._content_check_node)
        workflow.add_node("expression_check", self._expression_check_node)
        workflow.add_node("attribute_check", self._attribute_check_node)
        workflow.add_node("compliance_check", self._compliance_check_node)

        # 定义执行流程
        workflow.set_entry_point("content_check")
        workflow.add_edge("content_check", "expression_check")
        workflow.add_edge("expression_check", "attribute_check")
        workflow.add_edge("attribute_check", "compliance_check")
        workflow.add_edge("compliance_check", END)

        return workflow.compile()

    async def _content_check_node(self, state: VerificationState) -> VerificationState:
        """内容审核节点"""
        from app.services.agents.content_agent import ContentVerificationAgent
        agent = ContentVerificationAgent(config_manager.get("verification.content_check"))
        return await agent.execute(state)

    # 其他节点实现类似...
```

## 8. 部署和监控

### 8.1 部署要求
- **Python版本**: 3.10+
- **依赖管理**: 需要添加langgraph依赖
- **配置文件**: 扩展config.yaml
- **数据文件**: 敏感词库文件

### 8.2 性能监控
- **响应时间**: 监控各Agent执行时间
- **准确率**: 统计审核结果准确性
- **并发处理**: 监控系统并发处理能力

### 8.3 日志记录
- **审核日志**: 记录每次审核的详细过程
- **错误日志**: 记录审核过程中的异常
- **性能日志**: 记录各步骤的执行时间

## 9. 扩展性设计

### 9.1 新增Agent
- 继承BaseVerificationAgent基类
- 实现_verify方法
- 在工作流中添加新节点

### 9.2 配置扩展
- 在config.yaml中添加新的审核规则
- 支持动态加载配置
- 支持A/B测试不同配置

### 9.3 模型扩展
- 支持不同模型的审核策略
- 支持模型组合使用
- 支持自定义模型接入

这个技术方案基于现有项目架构，充分利用了已有的技术栈和设计模式，确保了与现有系统的良好集成。通过模块化设计和配置化管理，保证了系统的可扩展性和可维护性。
